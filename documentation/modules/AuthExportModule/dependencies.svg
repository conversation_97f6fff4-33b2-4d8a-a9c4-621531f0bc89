<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="828pt" height="284pt"
 viewBox="0.00 0.00 828.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 824,-280 824,4 -4,4"/>
<text text-anchor="start" x="389.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="176,-10 176,-30 196,-30 196,-10 176,-10"/>
<text text-anchor="start" x="199.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="289,-10 289,-30 309,-30 309,-10 289,-10"/>
<text text-anchor="start" x="312.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="375,-10 375,-30 395,-30 395,-10 375,-10"/>
<text text-anchor="start" x="398.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="472,-10 472,-30 492,-30 492,-10 472,-10"/>
<text text-anchor="start" x="495.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="568,-10 568,-30 588,-30 588,-10 568,-10"/>
<text text-anchor="start" x="591.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_AuthExportModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 812,-268 812,-70 8,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_AuthExportModule_imports</title>
<polygon fill="none" stroke="black" points="524,-78 524,-130 804,-130 804,-78 524,-78"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_AuthExportModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 516,-130 516,-78 16,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_AuthExportModule_exports</title>
<polygon fill="none" stroke="black" points="192,-208 192,-260 718,-260 718,-208 192,-208"/>
</g>
<!-- LoggerModule -->
<g id="node1" class="node">
<title>LoggerModule</title>
<polygon fill="#8dd3c7" stroke="black" points="795.98,-122 792.98,-126 771.98,-126 768.98,-122 696.02,-122 696.02,-86 795.98,-86 795.98,-122"/>
<text text-anchor="middle" x="746" y="-99.8" font-family="Times,serif" font-size="14.00">LoggerModule</text>
</g>
<!-- AuthExportModule -->
<g id="node3" class="node">
<title>AuthExportModule</title>
<polygon fill="#8dd3c7" stroke="black" points="533.65,-187 530.65,-191 509.65,-191 506.65,-187 408.35,-187 408.35,-151 533.65,-151 533.65,-187"/>
<text text-anchor="middle" x="471" y="-164.8" font-family="Times,serif" font-size="14.00">AuthExportModule</text>
</g>
<!-- LoggerModule&#45;&gt;AuthExportModule -->
<g id="edge1" class="edge">
<title>LoggerModule&#45;&gt;AuthExportModule</title>
<path fill="none" stroke="black" d="M702.79,-122.02C702.79,-139.37 702.79,-163 702.79,-163 702.79,-163 543.73,-163 543.73,-163"/>
<polygon fill="black" stroke="black" points="543.73,-159.5 533.73,-163 543.73,-166.5 543.73,-159.5"/>
</g>
<!-- QueryDatabaseModule -->
<g id="node2" class="node">
<title>QueryDatabaseModule</title>
<polygon fill="#8dd3c7" stroke="black" points="677.83,-122 674.83,-126 653.83,-126 650.83,-122 532.17,-122 532.17,-86 677.83,-86 677.83,-122"/>
<text text-anchor="middle" x="605" y="-99.8" font-family="Times,serif" font-size="14.00">QueryDatabaseModule</text>
</g>
<!-- QueryDatabaseModule&#45;&gt;AuthExportModule -->
<g id="edge2" class="edge">
<title>QueryDatabaseModule&#45;&gt;AuthExportModule</title>
<path fill="none" stroke="black" d="M531.92,-104C524.78,-104 520.11,-104 520.11,-104 520.11,-104 520.11,-140.89 520.11,-140.89"/>
<polygon fill="black" stroke="black" points="516.61,-140.89 520.11,-150.89 523.61,-140.89 516.61,-140.89"/>
</g>
<!-- AuthExportModule  -->
<g id="node4" class="node">
<title>AuthExportModule </title>
<polygon fill="#fb8072" stroke="black" points="709.65,-252 580.35,-252 580.35,-216 709.65,-216 709.65,-252"/>
<text text-anchor="middle" x="645" y="-229.8" font-family="Times,serif" font-size="14.00">AuthExportModule </text>
</g>
<!-- AuthExportModule&#45;&gt;AuthExportModule  -->
<g id="edge3" class="edge">
<title>AuthExportModule&#45;&gt;AuthExportModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M533.97,-175C578.08,-175 629.17,-175 629.17,-175 629.17,-175 629.17,-205.98 629.17,-205.98"/>
<polygon fill="black" stroke="black" points="625.67,-205.98 629.17,-215.98 632.67,-205.98 625.67,-205.98"/>
</g>
<!-- AuthQueryExportRepository  -->
<g id="node5" class="node">
<title>AuthQueryExportRepository </title>
<polygon fill="#fb8072" stroke="black" points="562.02,-252 379.98,-252 379.98,-216 562.02,-216 562.02,-252"/>
<text text-anchor="middle" x="471" y="-229.8" font-family="Times,serif" font-size="14.00">AuthQueryExportRepository </text>
</g>
<!-- AuthExportModule&#45;&gt;AuthQueryExportRepository  -->
<g id="edge4" class="edge">
<title>AuthExportModule&#45;&gt;AuthQueryExportRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M471,-187.11C471,-187.11 471,-205.99 471,-205.99"/>
<polygon fill="black" stroke="black" points="467.5,-205.99 471,-215.99 474.5,-205.99 467.5,-205.99"/>
</g>
<!-- AuthQueryExportService  -->
<g id="node6" class="node">
<title>AuthQueryExportService </title>
<polygon fill="#fb8072" stroke="black" points="362.06,-252 199.94,-252 199.94,-216 362.06,-216 362.06,-252"/>
<text text-anchor="middle" x="281" y="-229.8" font-family="Times,serif" font-size="14.00">AuthQueryExportService </text>
</g>
<!-- AuthExportModule&#45;&gt;AuthQueryExportService  -->
<g id="edge5" class="edge">
<title>AuthExportModule&#45;&gt;AuthQueryExportService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M408.19,-175C363.76,-175 312.07,-175 312.07,-175 312.07,-175 312.07,-205.98 312.07,-205.98"/>
<polygon fill="black" stroke="black" points="308.57,-205.98 312.07,-215.98 315.57,-205.98 308.57,-205.98"/>
</g>
<!-- AuthQueryExportRepository -->
<g id="node7" class="node">
<title>AuthQueryExportRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="385" cy="-104" rx="123.28" ry="18"/>
<text text-anchor="middle" x="385" y="-99.8" font-family="Times,serif" font-size="14.00">AuthQueryExportRepository</text>
</g>
<!-- AuthQueryExportRepository&#45;&gt;AuthExportModule -->
<g id="edge6" class="edge">
<title>AuthQueryExportRepository&#45;&gt;AuthExportModule</title>
<path fill="none" stroke="black" d="M458.16,-118.75C458.16,-118.75 458.16,-140.59 458.16,-140.59"/>
<polygon fill="black" stroke="black" points="454.66,-140.59 458.16,-150.59 461.66,-140.59 454.66,-140.59"/>
</g>
<!-- AuthQueryExportService -->
<g id="node8" class="node">
<title>AuthQueryExportService</title>
<ellipse fill="#fdb462" stroke="black" cx="134" cy="-104" rx="109.93" ry="18"/>
<text text-anchor="middle" x="134" y="-99.8" font-family="Times,serif" font-size="14.00">AuthQueryExportService</text>
</g>
<!-- AuthQueryExportService&#45;&gt;AuthExportModule -->
<g id="edge7" class="edge">
<title>AuthQueryExportService&#45;&gt;AuthExportModule</title>
<path fill="none" stroke="black" d="M221.72,-114.82C221.72,-131.85 221.72,-163 221.72,-163 221.72,-163 398.1,-163 398.1,-163"/>
<polygon fill="black" stroke="black" points="398.1,-166.5 408.1,-163 398.1,-159.5 398.1,-166.5"/>
</g>
</g>
</svg>
