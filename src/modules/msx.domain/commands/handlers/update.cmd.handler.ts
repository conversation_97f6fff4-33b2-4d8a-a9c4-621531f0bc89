import { EventPublisher, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { UpdateMsxCommand } from '../impl/update.cmd';
import { MsxEventStreamRepository } from '../../repository/event-stream.repository';
import { AggregateModel } from '../../models/aggregate.model';
import { MsxQueryRepository } from '../../../msx.queryside/repository/query.repository';
import { isNullOrUndefined } from 'util';
import { BaseEventStream } from '../../../shared/eventStream/models/base-event-stream.model';
import { CommonConst } from '../../../shared/constant/common.const';
import { MsxLoggerService } from '../../../logger/logger.service';
import moment = require('moment');

@CommandHandler(UpdateMsxCommand)
export class UpdateMsxCommandHandler implements
  ICommandHandler<UpdateMsxCommand> {
  private readonly context = UpdateMsxCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATES.MSX.NAME;
  private readonly eventName: string = CommonConst.AGGREGATES.MSX.UPDATED;
  constructor(
    private readonly repository: MsxEventStreamRepository,
    private readonly publisher: EventPublisher,
    private readonly loggerService: MsxLoggerService,
    private readonly queryRepository: MsxQueryRepository
  ) { }

  // execute Cteate Msx Command
  async execute(command: UpdateMsxCommand) {

    this.loggerService.log(this.context, `Async Update ${this.aggregateName} cmd ...`);
    const { messagePattern, id, commandModel } = command;
    const model: any = commandModel;

    // check duplicate value manually
    const duplicateValue = await this.queryRepository.checkDuplicateUpdate(
      {
        id: model.id,
        name: model.name
      }
    );
    if (duplicateValue) {
      this.loggerService.log(this.context, 'duplicateValue =>', duplicateValue);
      return;
    }

    model.modifiedDate = moment().toDate();

    const eventStream = new BaseEventStream();
    eventStream.id = id;
    eventStream.aggregateId = id;

    if (isNullOrUndefined(model.id)) model.id = id;
    eventStream.streamId = model.id;

    eventStream.aggregate = this.aggregateName;
    eventStream.eventName = this.eventName;

    model.eventName = this.eventName;
    if (isNullOrUndefined(model.actionName)) model.actionName = messagePattern;

    // aggregate model
    const aggregateModel = new AggregateModel(eventStream);
    const cmdPublisher = this.publisher.mergeObjectContext(aggregateModel);
    const msg = `${this.aggregateName}`;
    cmdPublisher.addItem(msg, model);
    cmdPublisher.commit();
  }
}
