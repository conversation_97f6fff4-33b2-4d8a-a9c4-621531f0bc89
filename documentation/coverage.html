<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-sts documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="./images/favicon.ico">
	      <link rel="stylesheet" href="./styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="./" class="navbar-brand">msx-sts documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content coverage">
                   <div class="content-data">














                   


<ol class="breadcrumb">
    <li>Documentation coverage</li>
</ol>

<div>
    <img src="./images/coverage-badge-documentation.svg">
</div>

<table class="table table-bordered coverage" id="coverage-table">
    <thead class="coverage-header">
        <tr>
            <th>File</th>
            <th>Type</th>
            <th>Identifier</th>
            <th style="text-align:right" class="statements" data-sort-default>Statements</th>
        </tr>
    </thead>
    <tbody>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#ac">src/app.module.ts</a>
            </td>
            <td>variable</td>
            <td>ac</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Roles">src/common/decorators/roles.decorator.ts</a>
            </td>
            <td>variable</td>
            <td>Roles</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/HttpExceptionFilter.html">src/common/filters/http-exception.filter.ts</a>
            </td>
            <td>class</td>
            <td>HttpExceptionFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExceptionFilter.html">src/common/filters/rpc-exception.filter.ts</a>
            </td>
            <td>class</td>
            <td>ExceptionFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./guards/RolesGuard.html">src/common/guards/roles.guard.ts</a>
            </td>
            <td>guard</td>
            <td>RolesGuard</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ErrorsInterceptor.html">src/common/interceptors/exception.interceptor.ts</a>
            </td>
            <td>injectable</td>
            <td>ErrorsInterceptor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LoggingInterceptor.html">src/common/interceptors/logging.interceptor.ts</a>
            </td>
            <td>injectable</td>
            <td>LoggingInterceptor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TimeoutInterceptor.html">src/common/interceptors/timeout.interceptor.ts</a>
            </td>
            <td>injectable</td>
            <td>TimeoutInterceptor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ParseIntPipe.html">src/common/pipes/parse-int.pipe.ts</a>
            </td>
            <td>injectable</td>
            <td>ParseIntPipe</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ValidationPipe.html">src/common/pipes/validation.pipe.ts</a>
            </td>
            <td>injectable</td>
            <td>ValidationPipe</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CryptographerService.html">src/common/services/cryptographer.service.ts</a>
            </td>
            <td>injectable</td>
            <td>CryptographerService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/Environments.html">src/environments/environment.ts</a>
            </td>
            <td>injectable</td>
            <td>Environments</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href=".//functions.html#bootstrap">src/main.ts</a>
            </td>
            <td>function</td>
            <td>bootstrap</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#configService">src/main.ts</a>
            </td>
            <td>variable</td>
            <td>configService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DuplicateEmailCommandHandler.html">src/modules/auth.domain/commands/handlers/duplicate-email.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>DuplicateEmailCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/auth.domain/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignInCommandHandler.html">src/modules/auth.domain/commands/handlers/sign-in.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>SignInCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignOutCommandHandler.html">src/modules/auth.domain/commands/handlers/sign-out.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>SignOutCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpCommandHandler.html">src/modules/auth.domain/commands/handlers/sign-up.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>SignUpCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DuplicateEmailCommand.html">src/modules/auth.domain/commands/impl/duplicate-email.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DuplicateEmailCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignInCommand.html">src/modules/auth.domain/commands/impl/sign-in.cmd.ts</a>
            </td>
            <td>class</td>
            <td>SignInCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignOutCommand.html">src/modules/auth.domain/commands/impl/sign-out.cmd.ts</a>
            </td>
            <td>class</td>
            <td>SignOutCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpCommand.html">src/modules/auth.domain/commands/impl/sign-up.cmd.ts</a>
            </td>
            <td>class</td>
            <td>SignUpCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AuthController.html">src/modules/auth.domain/controller.ts</a>
            </td>
            <td>controller</td>
            <td>AuthController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#jwt">src/modules/auth.domain/controller.ts</a>
            </td>
            <td>variable</td>
            <td>jwt</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LogInDto.html">src/modules/auth.domain/dto/auth.dto.ts</a>
            </td>
            <td>class</td>
            <td>LogInDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpDto.html">src/modules/auth.domain/dto/auth.dto.ts</a>
            </td>
            <td>class</td>
            <td>SignUpDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AuthEventHandler.html">src/modules/auth.domain/events/event-stream-created.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>AuthEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AuthEvent.html">src/modules/auth.domain/events/event-stream-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>AuthEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/auth.domain/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/JwtPayload.html">src/modules/auth.domain/interfaces/jwt-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>JwtPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AggregateModel.html">src/modules/auth.domain/models/aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>AggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/JwtStrategy.html">src/modules/auth.domain/passport/jwt.strategy.ts</a>
            </td>
            <td>injectable</td>
            <td>JwtStrategy</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#callback">src/modules/auth.domain/passport/jwt.strategy.ts</a>
            </td>
            <td>variable</td>
            <td>callback</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LocalStrategy.html">src/modules/auth.domain/passport/local.strategy.ts</a>
            </td>
            <td>injectable</td>
            <td>LocalStrategy</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#callback">src/modules/auth.domain/passport/local.strategy.ts</a>
            </td>
            <td>variable</td>
            <td>callback</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsProviders">src/modules/auth.domain/providers/cqrs.domain.providers.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AuthEventStreamRepository.html">src/modules/auth.domain/repository/event-stream.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>AuthEventStreamRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AuthSaga.html">src/modules/auth.domain/sagas/auth.saga.ts</a>
            </td>
            <td>injectable</td>
            <td>AuthSaga</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Sagas">src/modules/auth.domain/sagas/index.ts</a>
            </td>
            <td>variable</td>
            <td>Sagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/auth.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsDomainSchema">src/modules/auth.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsDomainSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#payloadSchema">src/modules/auth.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>payloadSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#roleSchema">src/modules/auth.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>roleSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/auth.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AuthService.html">src/modules/auth.domain/service.ts</a>
            </td>
            <td>injectable</td>
            <td>AuthService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/auth.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAuthQueryCommandHandler.html">src/modules/auth.queryside/commands/handlers/create-auth.query.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateAuthQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/auth.queryside/commands/handlers/create-auth.query.cmd.handler.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteAuthQueryCommandHandler.html">src/modules/auth.queryside/commands/handlers/delete-auth.query.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>DeleteAuthQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/auth.queryside/commands/handlers/delete-auth.query.cmd.handler.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#AuthQueryCommandHandlers">src/modules/auth.queryside/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>AuthQueryCommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpAuthQueryCommandHandler.html">src/modules/auth.queryside/commands/handlers/sign-up-auth.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>SignUpAuthQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAuthQueryCommandHandler.html">src/modules/auth.queryside/commands/handlers/update-auth.query.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAuthQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/auth.queryside/commands/handlers/update-auth.query.cmd.handler.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateAuthQueryCommand.html">src/modules/auth.queryside/commands/impl/create-auth.query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateAuthQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteAuthQueryCommand.html">src/modules/auth.queryside/commands/impl/delete-auth.query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DeleteAuthQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpAuthQueryCommand.html">src/modules/auth.queryside/commands/impl/sign-up-auth-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>SignUpAuthQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateAuthQueryCommand.html">src/modules/auth.queryside/commands/impl/update-auth.query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateAuthQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AuthQueryController.html">src/modules/auth.queryside/controller.ts</a>
            </td>
            <td>controller</td>
            <td>AuthQueryController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AuthQueryCreatedEvent.html">src/modules/auth.queryside/events/auth.query-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>AuthQueryCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AuthQueryDeletedEvent.html">src/modules/auth.queryside/events/auth.query-deleted.evt.ts</a>
            </td>
            <td>class</td>
            <td>AuthQueryDeletedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AuthQueryUpdatedEvent.html">src/modules/auth.queryside/events/auth.query-updated.evt.ts</a>
            </td>
            <td>class</td>
            <td>AuthQueryUpdatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AuthQuerySignedUpEvent.html">src/modules/auth.queryside/events/auth.query-user-signed-up.evt.ts</a>
            </td>
            <td>class</td>
            <td>AuthQuerySignedUpEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AuthQueryEventHandler.html">src/modules/auth.queryside/events/auth.query.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>AuthQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/auth.queryside/events/auth.query.evt.handler.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#AuthQueryEventHandlers">src/modules/auth.queryside/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>AuthQueryEventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AuthQueryExportRepository.html">src/modules/auth.queryside/export/repository.ts</a>
            </td>
            <td>injectable</td>
            <td>AuthQueryExportRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/auth.queryside/export/repository.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AuthQueryExportService.html">src/modules/auth.queryside/export/service.ts</a>
            </td>
            <td>injectable</td>
            <td>AuthQueryExportService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IAuthDocument.html">src/modules/auth.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IAuthDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueryAggregateModel.html">src/modules/auth.queryside/models/query-aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>QueryAggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/auth.queryside/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/AuthPublicController.html">src/modules/auth.queryside/public.controller.ts</a>
            </td>
            <td>controller</td>
            <td>AuthPublicController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AuthQueryRepository.html">src/modules/auth.queryside/repository/auth.query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>AuthQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/auth.queryside/repository/auth.query.repository.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#AuthQuerySchema">src/modules/auth.queryside/schemas/auth.query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>AuthQuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#SocialProviderSchema">src/modules/auth.queryside/schemas/auth.query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>SocialProviderSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AuthQueryService.html">src/modules/auth.queryside/service.ts</a>
            </td>
            <td>injectable</td>
            <td>AuthQueryService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ICodeGenerateDocument.html">src/modules/code-generate/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ICodeGenerateDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/code-generate/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CodeGenerateRepository.html">src/modules/code-generate/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>CodeGenerateRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/code-generate/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CodeGenerateService.html">src/modules/code-generate/service.ts</a>
            </td>
            <td>injectable</td>
            <td>CodeGenerateService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ConfigService.html">src/modules/config/config.service.ts</a>
            </td>
            <td>injectable</td>
            <td>ConfigService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#domainDatabaseProviders">src/modules/database/domain/domain.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>domainDatabaseProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#instance">src/modules/database/domain/domain.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>instance</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Mongoose">src/modules/database/domain/domain.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>Mongoose</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#mongoUrl">src/modules/database/domain/domain.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>mongoUrl</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#instance">src/modules/database/query/query.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>instance</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Mongoose">src/modules/database/query/query.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>Mongoose</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#mongoUrl">src/modules/database/query/query.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>mongoUrl</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#queryDatabaseProviders">src/modules/database/query/query.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>queryDatabaseProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateFeatureCommandHandler.html">src/modules/feature.domain/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateFeatureCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteFeatureCommandHandler.html">src/modules/feature.domain/commands/handlers/delete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>DeleteFeatureCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/feature.domain/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyFeatureCommandHandler.html">src/modules/feature.domain/commands/handlers/modify.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ModifyFeatureCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateFeatureCommandHandler.html">src/modules/feature.domain/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateFeatureCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateFeatureCommand.html">src/modules/feature.domain/commands/impl/create.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateFeatureCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteFeatureCommand.html">src/modules/feature.domain/commands/impl/delete.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DeleteFeatureCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyFeatureCommand.html">src/modules/feature.domain/commands/impl/modify.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ModifyFeatureCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateFeatureCommand.html">src/modules/feature.domain/commands/impl/update.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateFeatureCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/FeatureController.html">src/modules/feature.domain/controller.ts</a>
            </td>
            <td>controller</td>
            <td>FeatureController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FeatureDto.html">src/modules/feature.domain/dto/feature.dto.ts</a>
            </td>
            <td>class</td>
            <td>FeatureDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FeatureEventStreamCreatedEventHandler.html">src/modules/feature.domain/events/event-stream-created.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>FeatureEventStreamCreatedEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FeatureEventStreamCreated.html">src/modules/feature.domain/events/event-stream-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>FeatureEventStreamCreated</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/feature.domain/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AggregateModel.html">src/modules/feature.domain/models/aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>AggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsProviders">src/modules/feature.domain/providers/cqrs.domain.providers.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/FeatureEventStreamRepository.html">src/modules/feature.domain/repository/event-stream.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>FeatureEventStreamRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/FeatureSaga.html">src/modules/feature.domain/sagas/feature.saga.ts</a>
            </td>
            <td>injectable</td>
            <td>FeatureSaga</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Sagas">src/modules/feature.domain/sagas/index.ts</a>
            </td>
            <td>variable</td>
            <td>Sagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/feature.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsDomainSchema">src/modules/feature.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsDomainSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#payloadSchema">src/modules/feature.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>payloadSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/feature.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/FeatureService.html">src/modules/feature.domain/service.ts</a>
            </td>
            <td>injectable</td>
            <td>FeatureService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/feature.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateFeatureQueryCommandHandler.html">src/modules/feature.queryside/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateFeatureQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteFeatureQueryCommandHandler.html">src/modules/feature.queryside/commands/handlers/delete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>DeleteFeatureQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/feature.queryside/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyFeatureQueryCommandHandler.html">src/modules/feature.queryside/commands/handlers/modify.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ModifyFeatureQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateFeatureQueryCommandHandler.html">src/modules/feature.queryside/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateFeatureQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateFeatureQueryCommand.html">src/modules/feature.queryside/commands/impl/create-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateFeatureQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteFeatureQueryCommand.html">src/modules/feature.queryside/commands/impl/delete-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DeleteFeatureQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyFeatureQueryCommand.html">src/modules/feature.queryside/commands/impl/modify-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ModifyFeatureQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateFeatureQueryCommand.html">src/modules/feature.queryside/commands/impl/update-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateFeatureQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/FeatureController.html">src/modules/feature.queryside/controller.ts</a>
            </td>
            <td>controller</td>
            <td>FeatureController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/feature.queryside/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FeatureCreatedQueryEvent.html">src/modules/feature.queryside/events/query-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>FeatureCreatedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FeatureDeletedQueryEvent.html">src/modules/feature.queryside/events/query-deleted.evt.ts</a>
            </td>
            <td>class</td>
            <td>FeatureDeletedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FeatureModifiedQueryEvent.html">src/modules/feature.queryside/events/query-modified.evt.ts</a>
            </td>
            <td>class</td>
            <td>FeatureModifiedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FeatureUpdatedQueryEvent.html">src/modules/feature.queryside/events/query-updated.evt.ts</a>
            </td>
            <td>class</td>
            <td>FeatureUpdatedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FeatureQueryEventHandler.html">src/modules/feature.queryside/events/query.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>FeatureQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IFeatureDocument.html">src/modules/feature.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IFeatureDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueryAggregateModel.html">src/modules/feature.queryside/models/query-aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>QueryAggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/feature.queryside/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/FeatureQueryRepository.html">src/modules/feature.queryside/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>FeatureQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/feature.queryside/repository/query.repository.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/feature.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/FeatureService.html">src/modules/feature.queryside/service.ts</a>
            </td>
            <td>injectable</td>
            <td>FeatureService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/ListenerEmployeeController.html">src/modules/listener/listener-employee.controller.ts</a>
            </td>
            <td>controller</td>
            <td>ListenerEmployeeController</td>
            <td align="right" data-sort="16">
                <span class="coverage-percent">16 %</span>
                <span class="coverage-count">(1/6)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/ListenerScheduleController.html">src/modules/listener/listener-schedule.controller.ts</a>
            </td>
            <td>controller</td>
            <td>ListenerScheduleController</td>
            <td align="right" data-sort="100">
                <span class="coverage-percent">100 %</span>
                <span class="coverage-count">(2/2)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/ListenerStsController.html">src/modules/listener/listener-sts.controller.ts</a>
            </td>
            <td>controller</td>
            <td>ListenerStsController</td>
            <td align="right" data-sort="50">
                <span class="coverage-percent">50 %</span>
                <span class="coverage-count">(1/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/ListenerController.html">src/modules/listener/listener.controller.ts</a>
            </td>
            <td>controller</td>
            <td>ListenerController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/40)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MsxLoggerService.html">src/modules/logger/logger.service.ts</a>
            </td>
            <td>injectable</td>
            <td>MsxLoggerService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/MasterDataQueryController.html">src/modules/master/controller.ts</a>
            </td>
            <td>controller</td>
            <td>MasterDataQueryController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MasterDto.html">src/modules/master/dto/master.dto.ts</a>
            </td>
            <td>class</td>
            <td>MasterDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IMasterDocument.html">src/modules/master/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IMasterDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/master/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MasterQueryRepository.html">src/modules/master/repository/master.query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>MasterQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/master/repository/master.query.repository.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#MasterQuerySchema">src/modules/master/schemas/master.query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>MasterQuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MasterQueryService.html">src/modules/master/service.ts</a>
            </td>
            <td>injectable</td>
            <td>MasterQueryService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CareClient.html">src/modules/mgs-sender/care.client.ts</a>
            </td>
            <td>injectable</td>
            <td>CareClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CustomerClient.html">src/modules/mgs-sender/customer.client.ts</a>
            </td>
            <td>injectable</td>
            <td>CustomerClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/EmployeeClient.html">src/modules/mgs-sender/employee.client.ts</a>
            </td>
            <td>injectable</td>
            <td>EmployeeClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LoggerClient.html">src/modules/mgs-sender/logger.client.ts</a>
            </td>
            <td>injectable</td>
            <td>LoggerClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MailerClient.html">src/modules/mgs-sender/mailer.client.ts</a>
            </td>
            <td>injectable</td>
            <td>MailerClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#RABBITMQ_URL">src/modules/mgs-sender/mgs-sender.module.ts</a>
            </td>
            <td>variable</td>
            <td>RABBITMQ_URL</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MsgSenderService.html">src/modules/mgs-sender/mgs.sender.service.ts</a>
            </td>
            <td>injectable</td>
            <td>MsgSenderService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/NotificationClient.html">src/modules/mgs-sender/notification.client.ts</a>
            </td>
            <td>injectable</td>
            <td>NotificationClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/NotifierClient.html">src/modules/mgs-sender/notifier.client.ts</a>
            </td>
            <td>injectable</td>
            <td>NotifierClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/OrgchartClient.html">src/modules/mgs-sender/orgchart.client.ts</a>
            </td>
            <td>injectable</td>
            <td>OrgchartClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/PropertyClient.html">src/modules/mgs-sender/property.client.ts</a>
            </td>
            <td>injectable</td>
            <td>PropertyClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/StsClient.html">src/modules/mgs-sender/sts.client.ts</a>
            </td>
            <td>injectable</td>
            <td>StsClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateMsxCommandHandler.html">src/modules/msx.domain/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateMsxCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteMsxCommandHandler.html">src/modules/msx.domain/commands/handlers/delete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>DeleteMsxCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/msx.domain/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateMsxCommandHandler.html">src/modules/msx.domain/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateMsxCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateMsxCommand.html">src/modules/msx.domain/commands/impl/create.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateMsxCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteMsxCommand.html">src/modules/msx.domain/commands/impl/delete.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DeleteMsxCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateMsxCommand.html">src/modules/msx.domain/commands/impl/update.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateMsxCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/MsxController.html">src/modules/msx.domain/controller.ts</a>
            </td>
            <td>controller</td>
            <td>MsxController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MsxDto.html">src/modules/msx.domain/dto/msx.dto.ts</a>
            </td>
            <td>class</td>
            <td>MsxDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MsxEventStreamCreatedEventHandler.html">src/modules/msx.domain/events/event-stream-created.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>MsxEventStreamCreatedEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MsxEventStreamCreated.html">src/modules/msx.domain/events/event-stream-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>MsxEventStreamCreated</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/msx.domain/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AggregateModel.html">src/modules/msx.domain/models/aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>AggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsProviders">src/modules/msx.domain/providers/cqrs.domain.providers.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MsxEventStreamRepository.html">src/modules/msx.domain/repository/event-stream.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>MsxEventStreamRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Sagas">src/modules/msx.domain/sagas/index.ts</a>
            </td>
            <td>variable</td>
            <td>Sagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MsxDoaminSaga.html">src/modules/msx.domain/sagas/msx-doamin.saga.ts</a>
            </td>
            <td>injectable</td>
            <td>MsxDoaminSaga</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/msx.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsDomainSchema">src/modules/msx.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsDomainSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#payloadSchema">src/modules/msx.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>payloadSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/msx.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MsxService.html">src/modules/msx.domain/service.ts</a>
            </td>
            <td>injectable</td>
            <td>MsxService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateMsxQueryCommandHandler.html">src/modules/msx.queryside/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateMsxQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteMsxQueryCommandHandler.html">src/modules/msx.queryside/commands/handlers/delete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>DeleteMsxQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/msx.queryside/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateMsxQueryCommandHandler.html">src/modules/msx.queryside/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateMsxQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateMsxQueryCommand.html">src/modules/msx.queryside/commands/impl/create-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateMsxQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteMsxQueryCommand.html">src/modules/msx.queryside/commands/impl/delete-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DeleteMsxQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateMsxQueryCommand.html">src/modules/msx.queryside/commands/impl/update-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateMsxQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/MsxController.html">src/modules/msx.queryside/controller.ts</a>
            </td>
            <td>controller</td>
            <td>MsxController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/msx.queryside/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MsxCreatedQueryEvent.html">src/modules/msx.queryside/events/query-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>MsxCreatedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MsxDeletedQueryEvent.html">src/modules/msx.queryside/events/query-deleted.evt.ts</a>
            </td>
            <td>class</td>
            <td>MsxDeletedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MsxUpdatedQueryEvent.html">src/modules/msx.queryside/events/query-updated.evt.ts</a>
            </td>
            <td>class</td>
            <td>MsxUpdatedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MsxQueryEventHandler.html">src/modules/msx.queryside/events/query.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>MsxQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/msx.queryside/events/query.evt.handler.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IMsxDocument.html">src/modules/msx.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IMsxDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueryAggregateModel.html">src/modules/msx.queryside/models/query-aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>QueryAggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/msx.queryside/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MsxQueryRepository.html">src/modules/msx.queryside/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>MsxQueryRepository</td>
            <td align="right" data-sort="6">
                <span class="coverage-percent">6 %</span>
                <span class="coverage-count">(1/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./guards/RolesGuard.html">src/modules/msx.queryside/roles.guard.ts</a>
            </td>
            <td>guard</td>
            <td>RolesGuard</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Sagas">src/modules/msx.queryside/sagas/index.ts</a>
            </td>
            <td>variable</td>
            <td>Sagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MsxQuerySaga.html">src/modules/msx.queryside/sagas/msx-query.saga.ts</a>
            </td>
            <td>injectable</td>
            <td>MsxQuerySaga</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/msx.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MsxService.html">src/modules/msx.queryside/service.ts</a>
            </td>
            <td>injectable</td>
            <td>MsxService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/PatchDataController.html">src/modules/patch-data/patch-data.controller.ts</a>
            </td>
            <td>controller</td>
            <td>PatchDataController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/PatchDataService.html">src/modules/patch-data/patch-data.service.ts</a>
            </td>
            <td>injectable</td>
            <td>PatchDataService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/permission.domain/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyPermissionCommandHandler.html">src/modules/permission.domain/commands/handlers/modify-permission.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ModifyPermissionCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreatePermissionCommandHandler.html">src/modules/permission.domain/commands/handlers/set-permission.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreatePermissionCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyPermissionCommand.html">src/modules/permission.domain/commands/impl/modify-permission.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ModifyPermissionCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SetPermissionCommand.html">src/modules/permission.domain/commands/impl/set-permission.cmd.ts</a>
            </td>
            <td>class</td>
            <td>SetPermissionCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/PermissionController.html">src/modules/permission.domain/controller.ts</a>
            </td>
            <td>controller</td>
            <td>PermissionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PermissionDto.html">src/modules/permission.domain/dto/permission.dto.ts</a>
            </td>
            <td>class</td>
            <td>PermissionDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PermissionEventStreamCreatedEventHandler.html">src/modules/permission.domain/events/event-stream-created.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>PermissionEventStreamCreatedEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PermissionEventStreamCreated.html">src/modules/permission.domain/events/event-stream-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>PermissionEventStreamCreated</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/permission.domain/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AggregateModel.html">src/modules/permission.domain/models/aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>AggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsProviders">src/modules/permission.domain/providers/cqrs.domain.providers.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/PermissionEventStreamRepository.html">src/modules/permission.domain/repository/event-stream.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>PermissionEventStreamRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Sagas">src/modules/permission.domain/sagas/index.ts</a>
            </td>
            <td>variable</td>
            <td>Sagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/PermissionDomainSaga.html">src/modules/permission.domain/sagas/permission-domain.saga.ts</a>
            </td>
            <td>injectable</td>
            <td>PermissionDomainSaga</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/permission.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsDomainSchema">src/modules/permission.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsDomainSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#payloadSchema">src/modules/permission.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>payloadSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#permissionSchema">src/modules/permission.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>permissionSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/permission.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/PermissionService.html">src/modules/permission.domain/service.ts</a>
            </td>
            <td>injectable</td>
            <td>PermissionService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/permission.queryside/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SetPermissionQueryCommandHandler.html">src/modules/permission.queryside/commands/handlers/set-permission.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>SetPermissionQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SetPermissionQueryCommand.html">src/modules/permission.queryside/commands/impl/set-permission-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>SetPermissionQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/PermissionController.html">src/modules/permission.queryside/controller.ts</a>
            </td>
            <td>controller</td>
            <td>PermissionController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/permission.queryside/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PermissionSettedQueryEvent.html">src/modules/permission.queryside/events/query-permission-setted.evt.ts</a>
            </td>
            <td>class</td>
            <td>PermissionSettedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PermissionQueryEventHandler.html">src/modules/permission.queryside/events/query.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>PermissionQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IQueryDocument.html">src/modules/permission.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IQueryDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueryAggregateModel.html">src/modules/permission.queryside/models/query-aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>QueryAggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/permission.queryside/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/PermissionQueryRepository.html">src/modules/permission.queryside/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>PermissionQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Sagas">src/modules/permission.queryside/sagas/index.ts</a>
            </td>
            <td>variable</td>
            <td>Sagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/PermissionQuerySaga.html">src/modules/permission.queryside/sagas/permission-query.sagas.ts</a>
            </td>
            <td>injectable</td>
            <td>PermissionQuerySaga</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#permissionSchema">src/modules/permission.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>permissionSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/permission.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/PermissionService.html">src/modules/permission.queryside/service.ts</a>
            </td>
            <td>injectable</td>
            <td>PermissionService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/RedisCacheService.html">src/modules/redis-cache/service.ts</a>
            </td>
            <td>injectable</td>
            <td>RedisCacheService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateRoleCommandHandler.html">src/modules/role.domain/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateRoleCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteRoleCommandHandler.html">src/modules/role.domain/commands/handlers/delete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>DeleteRoleCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/role.domain/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateRoleCommandHandler.html">src/modules/role.domain/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateRoleCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateRoleCommand.html">src/modules/role.domain/commands/impl/create.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateRoleCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteRoleCommand.html">src/modules/role.domain/commands/impl/delete.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DeleteRoleCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateRoleCommand.html">src/modules/role.domain/commands/impl/update.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateRoleCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/RoleController.html">src/modules/role.domain/controller.ts</a>
            </td>
            <td>controller</td>
            <td>RoleController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RoleDto.html">src/modules/role.domain/dto/role.dto.ts</a>
            </td>
            <td>class</td>
            <td>RoleDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IPermissionStatement.html">src/modules/role.domain/dto/role.dto.ts</a>
            </td>
            <td>interface</td>
            <td>IPermissionStatement</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RoleEventStreamCreatedEventHandler.html">src/modules/role.domain/events/event-stream-created.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>RoleEventStreamCreatedEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RoleEventStreamCreated.html">src/modules/role.domain/events/event-stream-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>RoleEventStreamCreated</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/role.domain/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AggregateModel.html">src/modules/role.domain/models/aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>AggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsProviders">src/modules/role.domain/providers/cqrs.domain.providers.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/RoleEventStreamRepository.html">src/modules/role.domain/repository/event-stream.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>RoleEventStreamRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Sagas">src/modules/role.domain/sagas/index.ts</a>
            </td>
            <td>variable</td>
            <td>Sagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/RoleSaga.html">src/modules/role.domain/sagas/role.saga.ts</a>
            </td>
            <td>injectable</td>
            <td>RoleSaga</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/role.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsDomainSchema">src/modules/role.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsDomainSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#payloadSchema">src/modules/role.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>payloadSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/role.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/RoleService.html">src/modules/role.domain/service.ts</a>
            </td>
            <td>injectable</td>
            <td>RoleService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateRoleQueryCommandHandler.html">src/modules/role.queryside/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateRoleQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteRoleQueryCommandHandler.html">src/modules/role.queryside/commands/handlers/delete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>DeleteRoleQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/role.queryside/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateRoleQueryCommandHandler.html">src/modules/role.queryside/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateRoleQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateRoleQueryCommand.html">src/modules/role.queryside/commands/impl/create-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateRoleQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteRoleQueryCommand.html">src/modules/role.queryside/commands/impl/delete-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DeleteRoleQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateRoleQueryCommand.html">src/modules/role.queryside/commands/impl/update-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateRoleQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/RoleController.html">src/modules/role.queryside/controller.ts</a>
            </td>
            <td>controller</td>
            <td>RoleController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/role.queryside/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RoleCreatedQueryEvent.html">src/modules/role.queryside/events/query-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>RoleCreatedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RoleDeletedQueryEvent.html">src/modules/role.queryside/events/query-deleted.evt.ts</a>
            </td>
            <td>class</td>
            <td>RoleDeletedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RoleUpdatedQueryEvent.html">src/modules/role.queryside/events/query-updated.evt.ts</a>
            </td>
            <td>class</td>
            <td>RoleUpdatedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RoleQueryEventHandler.html">src/modules/role.queryside/events/query.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>RoleQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IRoleDocument.html">src/modules/role.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IRoleDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueryAggregateModel.html">src/modules/role.queryside/models/query-aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>QueryAggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/role.queryside/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/RolePublicController.html">src/modules/role.queryside/public-controller.ts</a>
            </td>
            <td>controller</td>
            <td>RolePublicController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/RoleQueryRepository.html">src/modules/role.queryside/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>RoleQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/22)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/role.queryside/repository/query.repository.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/role.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/RoleService.html">src/modules/role.queryside/service.ts</a>
            </td>
            <td>injectable</td>
            <td>RoleService</td>
            <td align="right" data-sort="10">
                <span class="coverage-percent">10 %</span>
                <span class="coverage-count">(1/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ClassBased.html">src/modules/shared/classes/class-based.ts</a>
            </td>
            <td>class</td>
            <td>ClassBased</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonUtils.html">src/modules/shared/classes/class-utils.ts</a>
            </td>
            <td>class</td>
            <td>CommonUtils</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RoleTrialDto.html">src/modules/shared/classes/role-trial-dto.ts</a>
            </td>
            <td>class</td>
            <td>RoleTrialDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CacheKeyConst.html">src/modules/shared/constant/cache.const.ts</a>
            </td>
            <td>class</td>
            <td>CacheKeyConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CmdPatternConst.html">src/modules/shared/constant/cmd-pattern.const.ts</a>
            </td>
            <td>class</td>
            <td>CmdPatternConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/16)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonConst.html">src/modules/shared/constant/common.const.ts</a>
            </td>
            <td>class</td>
            <td>CommonConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ErrorConst.html">src/modules/shared/constant/error.const.ts</a>
            </td>
            <td>class</td>
            <td>ErrorConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueueConst.html">src/modules/shared/constant/queue.const.ts</a>
            </td>
            <td>class</td>
            <td>QueueConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UrlConst.html">src/modules/shared/constant/url.const.ts</a>
            </td>
            <td>class</td>
            <td>UrlConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/51)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BaseEventHandler.html">src/modules/shared/eventStream/events/base-event-handler.model.ts</a>
            </td>
            <td>class</td>
            <td>BaseEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IEventStreamDocument.html">src/modules/shared/eventStream/interfaces/event-stream-document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IEventStreamDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IPayloadBased.html">src/modules/shared/eventStream/interfaces/payload-based.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IPayloadBased</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BaseEventStream.html">src/modules/shared/eventStream/models/base-event-stream.model.ts</a>
            </td>
            <td>class</td>
            <td>BaseEventStream</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommandModel.html">src/modules/shared/eventStream/models/command.model.ts</a>
            </td>
            <td>class</td>
            <td>CommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Payload.html">src/modules/shared/eventStream/models/payload.model.ts</a>
            </td>
            <td>class</td>
            <td>Payload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IBaseInterface.html">src/modules/shared/interfaces/base.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IBaseInterface</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IMaster.html">src/modules/shared/interfaces/master.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IMaster</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DataMessagePattern.html">src/modules/shared/interfaces/messaging-pattern.interface.ts</a>
            </td>
            <td>class</td>
            <td>DataMessagePattern</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ErrorMessagePattern.html">src/modules/shared/interfaces/messaging-pattern.interface.ts</a>
            </td>
            <td>class</td>
            <td>ErrorMessagePattern</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/InfoMessagePattern.html">src/modules/shared/interfaces/messaging-pattern.interface.ts</a>
            </td>
            <td>class</td>
            <td>InfoMessagePattern</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IMessagingPattern.html">src/modules/shared/interfaces/messaging-pattern.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IMessagingPattern</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IResult.html">src/modules/shared/interfaces/result.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IResult</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DuplicateEmailCommandModel.html">src/modules/shared/services/auth/impl/cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>DuplicateEmailCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/18)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpCommandModel.html">src/modules/shared/services/auth/impl/cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>SignUpCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/18)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILogIn.html">src/modules/shared/services/auth/interfaces/auth.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILogIn</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ISignUp.html">src/modules/shared/services/auth/interfaces/auth.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ISignUp</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FeatureCommandModel.html">src/modules/shared/services/feature/impl/cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>FeatureCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IFeature.html">src/modules/shared/services/feature/interfaces/feature.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IFeature</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IFeatureByIdRequest.html">src/modules/shared/services/feature/interfaces/feature.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IFeatureByIdRequest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IFeatureResponse.html">src/modules/shared/services/feature/interfaces/feature.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IFeatureResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/MsxCommandModel.html">src/modules/shared/services/msx/impl/cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>MsxCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/21)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IMsx.html">src/modules/shared/services/msx/interfaces/msx.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IMsx</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IMsxByIdRequest.html">src/modules/shared/services/msx/interfaces/msx.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IMsxByIdRequest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IMsxResponse.html">src/modules/shared/services/msx/interfaces/msx.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IMsxResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PermissionCommandModel.html">src/modules/shared/services/permission/impl/cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>PermissionCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Permission.html">src/modules/shared/services/permission/interfaces/permission.interface.ts</a>
            </td>
            <td>class</td>
            <td>Permission</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IAclByFeatureIdRequest.html">src/modules/shared/services/permission/interfaces/permission.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IAclByFeatureIdRequest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IAclResponse.html">src/modules/shared/services/permission/interfaces/permission.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IAclResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IPermission.html">src/modules/shared/services/permission/interfaces/permission.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IPermission</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IPermissionByIdRequest.html">src/modules/shared/services/permission/interfaces/permission.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IPermissionByIdRequest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IPermissionResponse.html">src/modules/shared/services/permission/interfaces/permission.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IPermissionResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IPermissionStatement.html">src/modules/shared/services/permission/interfaces/permission.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IPermissionStatement</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RoleCommandModel.html">src/modules/shared/services/role/impl/cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>RoleCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IRole.html">src/modules/shared/services/role/interfaces/role.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IRole</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IRoleByIdRequest.html">src/modules/shared/services/role/interfaces/role.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IRoleByIdRequest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IRoleResponse.html">src/modules/shared/services/role/interfaces/role.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IRoleResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Usr">src/modules/shared/services/user/decorator/user.decorator.ts</a>
            </td>
            <td>variable</td>
            <td>Usr</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyUserPermissionCommandModel.html">src/modules/shared/services/user/impl/cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>ModifyUserPermissionCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SetUserRoleCommandModel.html">src/modules/shared/services/user/impl/cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>SetUserRoleCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpUserCommandModel.html">src/modules/shared/services/user/impl/cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>SignUpUserCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IUserResponse.html">src/modules/shared/services/user/interfaces/user-by-id.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IUserResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IUserResquest.html">src/modules/shared/services/user/interfaces/user-by-id.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IUserResquest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ISignUpUser.html">src/modules/shared/services/user/interfaces/user.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ISignUpUser</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IUser.html">src/modules/shared/services/user/interfaces/user.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IUser</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IUserRole.html">src/modules/shared/services/user/interfaces/user.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IUserRole</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateUserCommandHandler.html">src/modules/user.domain/commands/handlers/create-user.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateUserCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/user.domain/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyUserPermissionCommandHandler.html">src/modules/user.domain/commands/handlers/modify-permission.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ModifyUserPermissionCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SetUserRoleCommandHandler.html">src/modules/user.domain/commands/handlers/set-user-role.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>SetUserRoleCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpUserCommandHandler.html">src/modules/user.domain/commands/handlers/sign-up-user.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>SignUpUserCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateUserCommand.html">src/modules/user.domain/commands/impl/create-user.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateUserCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyUserPermissionCommand.html">src/modules/user.domain/commands/impl/modify-user-permission.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ModifyUserPermissionCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SetUserRoleCommand.html">src/modules/user.domain/commands/impl/set-user-role.cmd.ts</a>
            </td>
            <td>class</td>
            <td>SetUserRoleCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpUserCommand.html">src/modules/user.domain/commands/impl/sign-up-user.cmd.ts</a>
            </td>
            <td>class</td>
            <td>SignUpUserCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/UserController.html">src/modules/user.domain/controller.ts</a>
            </td>
            <td>controller</td>
            <td>UserController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/36)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#jwt">src/modules/user.domain/controller.ts</a>
            </td>
            <td>variable</td>
            <td>jwt</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AgencyDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>AgencyDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/21)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ChangeEmailDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>ChangeEmailDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ChangePhoneDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>ChangePhoneDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ChangePwdByPhoneDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>ChangePwdByPhoneDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ChangePwdDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>ChangePwdDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CheckIsExitDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>CheckIsExitDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateOtpDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateOtpDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomerDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>CustomerDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/20)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ForgotPwdDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>ForgotPwdDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ForgotPwdMobileDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>ForgotPwdMobileDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OtpLoginMobileDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>OtpLoginMobileDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RegisterAdvisorsDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>RegisterAdvisorsDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RegisterEmailDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>RegisterEmailDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RegisterEventDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>RegisterEventDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RequestDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>RequestDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ResetPasswordDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>ResetPasswordDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ResetPwdDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>ResetPwdDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SetUserRoleDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>SetUserRoleDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>UserDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/VerifyCodeDto.html">src/modules/user.domain/dto/user.dto.ts</a>
            </td>
            <td>class</td>
            <td>VerifyCodeDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserCreatedEventHandler.html">src/modules/user.domain/events/event-stream.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>UserCreatedEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserCreatedEvent.html">src/modules/user.domain/events/event-stream.evt.ts</a>
            </td>
            <td>class</td>
            <td>UserCreatedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserEvent.html">src/modules/user.domain/events/event-stream.evt.ts</a>
            </td>
            <td>class</td>
            <td>UserEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserPermissionModifiedEvent.html">src/modules/user.domain/events/event-stream.evt.ts</a>
            </td>
            <td>class</td>
            <td>UserPermissionModifiedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserRoleSettedEvent.html">src/modules/user.domain/events/event-stream.evt.ts</a>
            </td>
            <td>class</td>
            <td>UserRoleSettedEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/user.domain/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AggregateModel.html">src/modules/user.domain/models/aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>AggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsProviders">src/modules/user.domain/providers/cqrs.domain.providers.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/UserEventStreamRepository.html">src/modules/user.domain/repository/event-stream.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>UserEventStreamRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Sagas">src/modules/user.domain/sagas/index.ts</a>
            </td>
            <td>variable</td>
            <td>Sagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ModifyUserRoleSaga.html">src/modules/user.domain/sagas/modify-user-role.saga.ts</a>
            </td>
            <td>injectable</td>
            <td>ModifyUserRoleSaga</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/SetUserRoleSaga.html">src/modules/user.domain/sagas/set-user-role.saga.ts</a>
            </td>
            <td>injectable</td>
            <td>SetUserRoleSaga</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/UserCreatedSaga.html">src/modules/user.domain/sagas/user-created.saga.ts</a>
            </td>
            <td>injectable</td>
            <td>UserCreatedSaga</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/user.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsDomainSchema">src/modules/user.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsDomainSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#payloadSchema">src/modules/user.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>payloadSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#permissionSchema">src/modules/user.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>permissionSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#rolesSchema">src/modules/user.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>rolesSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/user.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/UserDomainService.html">src/modules/user.domain/service.ts</a>
            </td>
            <td>injectable</td>
            <td>UserDomainService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/56)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#_">src/modules/user.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>_</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#generator">src/modules/user.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>generator</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#jwt">src/modules/user.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>jwt</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#request">src/modules/user.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>request</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/user.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/UserAuthController.html">src/modules/user.queryside/auth.controller.ts</a>
            </td>
            <td>controller</td>
            <td>UserAuthController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateUserQueryCommandHandler.html">src/modules/user.queryside/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateUserQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteUserQueryCommandHandler.html">src/modules/user.queryside/commands/handlers/delete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>DeleteUserQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/user.queryside/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyUserPermissionQueryCommandHandler.html">src/modules/user.queryside/commands/handlers/modify-user-permission.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ModifyUserPermissionQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SetUserRoleQueryCommandHandler.html">src/modules/user.queryside/commands/handlers/set-user-role.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>SetUserRoleQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpUserQueryCommandHandler.html">src/modules/user.queryside/commands/handlers/sign-up-user.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>SignUpUserQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateUserQueryCommandHandler.html">src/modules/user.queryside/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateUserQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateUserQueryCommand.html">src/modules/user.queryside/commands/impl/create-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateUserQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteUserQueryCommand.html">src/modules/user.queryside/commands/impl/delete-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DeleteUserQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ModifyUserPermissionQueryCommand.html">src/modules/user.queryside/commands/impl/modify-user-permission-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ModifyUserPermissionQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SetUserRoleQueryCommand.html">src/modules/user.queryside/commands/impl/set-user-role-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>SetUserRoleQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SignUpUserQueryCommand.html">src/modules/user.queryside/commands/impl/sign-up-user-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>SignUpUserQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateUserQueryCommand.html">src/modules/user.queryside/commands/impl/update-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateUserQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/UserController.html">src/modules/user.queryside/controller.ts</a>
            </td>
            <td>controller</td>
            <td>UserController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/user.queryside/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserCreatedQueryEvent.html">src/modules/user.queryside/events/query-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>UserCreatedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserDeletedQueryEvent.html">src/modules/user.queryside/events/query-deleted.evt.ts</a>
            </td>
            <td>class</td>
            <td>UserDeletedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserUpdatedQueryEvent.html">src/modules/user.queryside/events/query-updated.evt.ts</a>
            </td>
            <td>class</td>
            <td>UserUpdatedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserPermissionModifiedQueryEvent.html">src/modules/user.queryside/events/query-user-permission-modified.evt.ts</a>
            </td>
            <td>class</td>
            <td>UserPermissionModifiedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserRoleSettedQueryEvent.html">src/modules/user.queryside/events/query-user-role-setted.evt.ts</a>
            </td>
            <td>class</td>
            <td>UserRoleSettedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserSignedUpQueryEvent.html">src/modules/user.queryside/events/query-user-signed-up.evt.ts</a>
            </td>
            <td>class</td>
            <td>UserSignedUpQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserQueryEventHandler.html">src/modules/user.queryside/events/query.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>UserQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/UserExportRepository.html">src/modules/user.queryside/export/repository.ts</a>
            </td>
            <td>injectable</td>
            <td>UserExportRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/user.queryside/export/repository.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/UserExportService.html">src/modules/user.queryside/export/service.ts</a>
            </td>
            <td>injectable</td>
            <td>UserExportService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IUserDocument.html">src/modules/user.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IUserDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueryAggregateModel.html">src/modules/user.queryside/models/query-aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>QueryAggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/user.queryside/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/UserQueryRepository.html">src/modules/user.queryside/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>UserQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/46)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/user.queryside/repository/query.repository.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#permissionSchema">src/modules/user.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>permissionSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#PosSchema">src/modules/user.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>PosSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/user.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#rolesSchema">src/modules/user.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>rolesSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#UserSchema">src/modules/user.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>UserSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/UserService.html">src/modules/user.queryside/service.ts</a>
            </td>
            <td>injectable</td>
            <td>UserService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/25)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserPagingResponseDto.html">src/modules/user.queryside/web/dto/response.dto.ts</a>
            </td>
            <td>class</td>
            <td>UserPagingResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UserResponseDto.html">src/modules/user.queryside/web/dto/response.dto.ts</a>
            </td>
            <td>class</td>
            <td>UserResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/16)</span>
            </td>
        </tr>
    </tbody>
</table>

<script src="js/libs/tablesort.min.js"></script>
<script src="js/libs/tablesort.number.min.js"></script>
<script>
    new Tablesort(document.getElementById('coverage-table'));
</script>

                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 0;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'coverage';
            var COMPODOC_CURRENT_PAGE_URL = 'coverage.html';
       </script>

       <script src="./js/libs/custom-elements.min.js"></script>
       <script src="./js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="./js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="./js/menu-wc.js" defer></script>

       <script src="./js/libs/bootstrap-native.js"></script>

       <script src="./js/libs/es6-shim.min.js"></script>
       <script src="./js/libs/EventDispatcher.js"></script>
       <script src="./js/libs/promise.min.js"></script>
       <script src="./js/libs/zepto.min.js"></script>

       <script src="./js/compodoc.js"></script>

       <script src="./js/tabs.js"></script>
       <script src="./js/menu.js"></script>
       <script src="./js/libs/clipboard.min.js"></script>
       <script src="./js/libs/prism.js"></script>
       <script src="./js/sourceCode.js"></script>
          <script src="./js/search/search.js"></script>
          <script src="./js/search/lunr.min.js"></script>
          <script src="./js/search/search-lunr.js"></script>
          <script src="./js/search/search_index.js"></script>
       <script src="./js/lazy-load-graphs.js"></script>


    </body>
</html>
