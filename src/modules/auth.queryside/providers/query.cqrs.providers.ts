import { Connection } from "mongoose";
import { AuthQuerySchema } from "../schemas/auth.query.schema";
import { CommonConst } from "../../shared/constant/common.const";

export const QueryProviders = [
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) =>
      connection.model(CommonConst.AUTH_COLLECTION, AuthQuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
];
