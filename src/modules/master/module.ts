import { CqrsModule } from '@nestjs/cqrs';
import { MasterQueryService } from './service';
import { MasterQueryRepository } from './repository/master.query.repository';
import { QueryDatabaseModule } from '../database/query/query.database.module';
import { QueryProviders } from './providers/query.cqrs.providers';
import { Module } from '@nestjs/common';
import { LoggerModule } from '../logger/logger.module';
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';
import { MasterDataQueryController } from './controller';
import { RedisCacheModule } from '../redis-cache/module';

@Module({
  imports: [CqrsModule, QueryDatabaseModule, LoggerModule, MgsSenderModule, RedisCacheModule],
  controllers: [MasterDataQueryController],
  providers: [MasterQueryService, MasterQueryRepository, ...QueryProviders],
  exports: [MasterQueryRepository, MasterQuerySideModule, MasterQueryService],
})
export class MasterQuerySideModule {}
