<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="1154pt" height="284pt"
 viewBox="0.00 0.00 1154.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 1150,-280 1150,4 -4,4"/>
<text text-anchor="start" x="552.01" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="339,-10 339,-30 359,-30 359,-10 339,-10"/>
<text text-anchor="start" x="362.63" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="452,-10 452,-30 472,-30 472,-10 452,-10"/>
<text text-anchor="start" x="475.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="538,-10 538,-30 558,-30 558,-10 538,-10"/>
<text text-anchor="start" x="561.78" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="635,-10 635,-30 655,-30 655,-10 635,-10"/>
<text text-anchor="start" x="658.67" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="731,-10 731,-30 751,-30 751,-10 731,-10"/>
<text text-anchor="start" x="754.73" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_MasterQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 1138,-268 1138,-70 8,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_MasterQuerySideModule_imports</title>
<polygon fill="none" stroke="black" points="448,-78 448,-130 1130,-130 1130,-78 448,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_MasterQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="417,-208 417,-260 921,-260 921,-208 417,-208"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_MasterQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 440,-130 440,-78 16,-78"/>
</g>
<!-- ConfigModule -->
<g id="node1" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1122.44,-122 1119.44,-126 1098.44,-126 1095.44,-122 1023.56,-122 1023.56,-86 1122.44,-86 1122.44,-122"/>
<text text-anchor="middle" x="1073" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- MasterQuerySideModule -->
<g id="node6" class="node">
<title>MasterQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="752.29,-187 749.29,-191 728.29,-191 725.29,-187 593.71,-187 593.71,-151 752.29,-151 752.29,-187"/>
<text text-anchor="middle" x="673" y="-164.8" font-family="Times,serif" font-size="14.00">MasterQuerySideModule</text>
</g>
<!-- ConfigModule&#45;&gt;MasterQuerySideModule -->
<g id="edge1" class="edge">
<title>ConfigModule&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M1073,-122.29C1073,-144.21 1073,-178 1073,-178 1073,-178 762.51,-178 762.51,-178"/>
<polygon fill="black" stroke="black" points="762.51,-174.5 752.51,-178 762.51,-181.5 762.51,-174.5"/>
</g>
<!-- LoggerModule -->
<g id="node2" class="node">
<title>LoggerModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1005.98,-122 1002.98,-126 981.98,-126 978.98,-122 906.02,-122 906.02,-86 1005.98,-86 1005.98,-122"/>
<text text-anchor="middle" x="956" y="-99.8" font-family="Times,serif" font-size="14.00">LoggerModule</text>
</g>
<!-- LoggerModule&#45;&gt;MasterQuerySideModule -->
<g id="edge2" class="edge">
<title>LoggerModule&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M909.7,-122.11C909.7,-141.34 909.7,-169 909.7,-169 909.7,-169 762.15,-169 762.15,-169"/>
<polygon fill="black" stroke="black" points="762.15,-165.5 752.15,-169 762.15,-172.5 762.15,-165.5"/>
</g>
<!-- MgsSenderModule -->
<g id="node3" class="node">
<title>MgsSenderModule</title>
<polygon fill="#8dd3c7" stroke="black" points="887.81,-122 884.81,-126 863.81,-126 860.81,-122 764.19,-122 764.19,-86 887.81,-86 887.81,-122"/>
<text text-anchor="middle" x="826" y="-99.8" font-family="Times,serif" font-size="14.00">MgsSenderModule</text>
</g>
<!-- MgsSenderModule&#45;&gt;MasterQuerySideModule -->
<g id="edge3" class="edge">
<title>MgsSenderModule&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M826,-122.03C826,-138.4 826,-160 826,-160 826,-160 762.19,-160 762.19,-160"/>
<polygon fill="black" stroke="black" points="762.19,-156.5 752.19,-160 762.19,-163.5 762.19,-156.5"/>
</g>
<!-- QueryDatabaseModule -->
<g id="node4" class="node">
<title>QueryDatabaseModule</title>
<polygon fill="#8dd3c7" stroke="black" points="745.83,-122 742.83,-126 721.83,-126 718.83,-122 600.17,-122 600.17,-86 745.83,-86 745.83,-122"/>
<text text-anchor="middle" x="673" y="-99.8" font-family="Times,serif" font-size="14.00">QueryDatabaseModule</text>
</g>
<!-- QueryDatabaseModule&#45;&gt;MasterQuerySideModule -->
<g id="edge4" class="edge">
<title>QueryDatabaseModule&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M673,-122.11C673,-122.11 673,-140.99 673,-140.99"/>
<polygon fill="black" stroke="black" points="669.5,-140.99 673,-150.99 676.5,-140.99 669.5,-140.99"/>
</g>
<!-- RedisCacheModule -->
<g id="node5" class="node">
<title>RedisCacheModule</title>
<polygon fill="#8dd3c7" stroke="black" points="582.42,-122 579.42,-126 558.42,-126 555.42,-122 455.58,-122 455.58,-86 582.42,-86 582.42,-122"/>
<text text-anchor="middle" x="519" y="-99.8" font-family="Times,serif" font-size="14.00">RedisCacheModule</text>
</g>
<!-- RedisCacheModule&#45;&gt;MasterQuerySideModule -->
<g id="edge5" class="edge">
<title>RedisCacheModule&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M519,-122.03C519,-138.4 519,-160 519,-160 519,-160 583.74,-160 583.74,-160"/>
<polygon fill="black" stroke="black" points="583.74,-163.5 593.74,-160 583.74,-156.5 583.74,-163.5"/>
</g>
<!-- MasterQueryRepository  -->
<g id="node7" class="node">
<title>MasterQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="913.29,-252 758.71,-252 758.71,-216 913.29,-216 913.29,-252"/>
<text text-anchor="middle" x="836" y="-229.8" font-family="Times,serif" font-size="14.00">MasterQueryRepository </text>
</g>
<!-- MasterQuerySideModule&#45;&gt;MasterQueryRepository  -->
<g id="edge6" class="edge">
<title>MasterQuerySideModule&#45;&gt;MasterQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M746.4,-187.11C746.4,-206.34 746.4,-234 746.4,-234 746.4,-234 748.49,-234 748.49,-234"/>
<polygon fill="black" stroke="black" points="748.49,-237.5 758.49,-234 748.49,-230.5 748.49,-237.5"/>
</g>
<!-- MasterQueryService  -->
<g id="node8" class="node">
<title>MasterQueryService </title>
<polygon fill="#fb8072" stroke="black" points="740.83,-252 605.17,-252 605.17,-216 740.83,-216 740.83,-252"/>
<text text-anchor="middle" x="673" y="-229.8" font-family="Times,serif" font-size="14.00">MasterQueryService </text>
</g>
<!-- MasterQuerySideModule&#45;&gt;MasterQueryService  -->
<g id="edge7" class="edge">
<title>MasterQuerySideModule&#45;&gt;MasterQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M673,-187.11C673,-187.11 673,-205.99 673,-205.99"/>
<polygon fill="black" stroke="black" points="669.5,-205.99 673,-215.99 676.5,-205.99 669.5,-205.99"/>
</g>
<!-- MasterQuerySideModule  -->
<g id="node9" class="node">
<title>MasterQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="586.79,-252 425.21,-252 425.21,-216 586.79,-216 586.79,-252"/>
<text text-anchor="middle" x="506" y="-229.8" font-family="Times,serif" font-size="14.00">MasterQuerySideModule </text>
</g>
<!-- MasterQuerySideModule&#45;&gt;MasterQuerySideModule  -->
<g id="edge8" class="edge">
<title>MasterQuerySideModule&#45;&gt;MasterQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M599.6,-187.11C599.6,-206.34 599.6,-234 599.6,-234 599.6,-234 596.97,-234 596.97,-234"/>
<polygon fill="black" stroke="black" points="596.97,-230.5 586.97,-234 596.97,-237.5 596.97,-230.5"/>
</g>
<!-- MasterQueryRepository -->
<g id="node10" class="node">
<title>MasterQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="328" cy="-104" rx="104.21" ry="18"/>
<text text-anchor="middle" x="328" y="-99.8" font-family="Times,serif" font-size="14.00">MasterQueryRepository</text>
</g>
<!-- MasterQueryRepository&#45;&gt;MasterQuerySideModule -->
<g id="edge9" class="edge">
<title>MasterQueryRepository&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M428.73,-109.1C428.73,-124.42 428.73,-169 428.73,-169 428.73,-169 583.69,-169 583.69,-169"/>
<polygon fill="black" stroke="black" points="583.69,-172.5 593.69,-169 583.69,-165.5 583.69,-172.5"/>
</g>
<!-- MasterQueryService -->
<g id="node11" class="node">
<title>MasterQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="115" cy="-104" rx="90.87" ry="18"/>
<text text-anchor="middle" x="115" y="-99.8" font-family="Times,serif" font-size="14.00">MasterQueryService</text>
</g>
<!-- MasterQueryService&#45;&gt;MasterQuerySideModule -->
<g id="edge10" class="edge">
<title>MasterQueryService&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M115,-122.29C115,-144.21 115,-178 115,-178 115,-178 583.64,-178 583.64,-178"/>
<polygon fill="black" stroke="black" points="583.64,-181.5 593.64,-178 583.64,-174.5 583.64,-181.5"/>
</g>
</g>
</svg>
