<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-sts documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-sts documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content module">
                   <div class="content-data">



<ol class="breadcrumb">
    <li>Modules</li>
    <li>FeatureQuerySideModule</li>
</ol>

<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="875pt" height="284pt"
 viewBox="0.00 0.00 875.00 284.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 280)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-280 871,-280 871,4 -4,4"/>
<text text-anchor="start" x="412.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="199.5,-10 199.5,-30 219.5,-30 219.5,-10 199.5,-10"/>
<text text-anchor="start" x="223.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="312.5,-10 312.5,-30 332.5,-30 332.5,-10 312.5,-10"/>
<text text-anchor="start" x="336.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="398.5,-10 398.5,-30 418.5,-30 418.5,-10 398.5,-10"/>
<text text-anchor="start" x="422.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="495.5,-10 495.5,-30 515.5,-30 515.5,-10 495.5,-10"/>
<text text-anchor="start" x="519.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="591.5,-10 591.5,-30 611.5,-30 611.5,-10 591.5,-10"/>
<text text-anchor="start" x="615.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_FeatureQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-70 8,-268 859,-268 859,-70 8,-70"/>
</g>
<g id="clust6" class="cluster">
<title>cluster_FeatureQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="466,-78 466,-130 851,-130 851,-78 466,-78"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_FeatureQuerySideModule_imports</title>
<polygon fill="none" stroke="black" points="16,-78 16,-130 458,-130 458,-78 16,-78"/>
</g>
<g id="clust4" class="cluster">
<title>cluster_FeatureQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="188,-208 188,-260 666,-260 666,-208 188,-208"/>
</g>
<!-- LoggerModule -->
<g id="node1" class="node">
<title>LoggerModule</title>
<polygon fill="#8dd3c7" stroke="black" points="449.98,-122 446.98,-126 425.98,-126 422.98,-122 350.02,-122 350.02,-86 449.98,-86 449.98,-122"/>
<text text-anchor="middle" x="400" y="-99.8" font-family="Times,serif" font-size="14.00">LoggerModule</text>
</g>
<!-- FeatureQuerySideModule -->
<g id="node4" class="node">
<title>FeatureQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="480.9,-187 477.9,-191 456.9,-191 453.9,-187 319.1,-187 319.1,-151 480.9,-151 480.9,-187"/>
<text text-anchor="middle" x="400" y="-164.8" font-family="Times,serif" font-size="14.00">FeatureQuerySideModule</text>
</g>
<!-- LoggerModule&#45;&gt;FeatureQuerySideModule -->
<g id="edge1" class="edge">
<title>LoggerModule&#45;&gt;FeatureQuerySideModule</title>
<path fill="none" stroke="black" d="M400,-122.11C400,-122.11 400,-140.99 400,-140.99"/>
<polygon fill="black" stroke="black" points="396.5,-140.99 400,-150.99 403.5,-140.99 396.5,-140.99"/>
</g>
<!-- MsxQuerySideModule -->
<g id="node2" class="node">
<title>MsxQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="332.31,-122 329.31,-126 308.31,-126 305.31,-122 187.69,-122 187.69,-86 332.31,-86 332.31,-122"/>
<text text-anchor="middle" x="260" y="-99.8" font-family="Times,serif" font-size="14.00">MsxQuerySideModule</text>
</g>
<!-- MsxQuerySideModule&#45;&gt;FeatureQuerySideModule -->
<g id="edge2" class="edge">
<title>MsxQuerySideModule&#45;&gt;FeatureQuerySideModule</title>
<path fill="none" stroke="black" d="M325.73,-122.11C325.73,-122.11 325.73,-140.99 325.73,-140.99"/>
<polygon fill="black" stroke="black" points="322.23,-140.99 325.73,-150.99 329.23,-140.99 322.23,-140.99"/>
</g>
<!-- QueryDatabaseModule -->
<g id="node3" class="node">
<title>QueryDatabaseModule</title>
<polygon fill="#8dd3c7" stroke="black" points="169.83,-122 166.83,-126 145.83,-126 142.83,-122 24.17,-122 24.17,-86 169.83,-86 169.83,-122"/>
<text text-anchor="middle" x="97" y="-99.8" font-family="Times,serif" font-size="14.00">QueryDatabaseModule</text>
</g>
<!-- QueryDatabaseModule&#45;&gt;FeatureQuerySideModule -->
<g id="edge3" class="edge">
<title>QueryDatabaseModule&#45;&gt;FeatureQuerySideModule</title>
<path fill="none" stroke="black" d="M97,-122.02C97,-139.37 97,-163 97,-163 97,-163 309.2,-163 309.2,-163"/>
<polygon fill="black" stroke="black" points="309.2,-166.5 319.2,-163 309.2,-159.5 309.2,-166.5"/>
</g>
<!-- FeatureQueryRepository  -->
<g id="node5" class="node">
<title>FeatureQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="657.9,-252 500.1,-252 500.1,-216 657.9,-216 657.9,-252"/>
<text text-anchor="middle" x="579" y="-229.8" font-family="Times,serif" font-size="14.00">FeatureQueryRepository </text>
</g>
<!-- FeatureQuerySideModule&#45;&gt;FeatureQueryRepository  -->
<g id="edge4" class="edge">
<title>FeatureQuerySideModule&#45;&gt;FeatureQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M480.74,-178C518.81,-178 556,-178 556,-178 556,-178 556,-205.97 556,-205.97"/>
<polygon fill="black" stroke="black" points="552.5,-205.97 556,-215.97 559.5,-205.97 552.5,-205.97"/>
</g>
<!-- FeatureQuerySideModule  -->
<g id="node6" class="node">
<title>FeatureQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="482.4,-252 317.6,-252 317.6,-216 482.4,-216 482.4,-252"/>
<text text-anchor="middle" x="400" y="-229.8" font-family="Times,serif" font-size="14.00">FeatureQuerySideModule </text>
</g>
<!-- FeatureQuerySideModule&#45;&gt;FeatureQuerySideModule  -->
<g id="edge5" class="edge">
<title>FeatureQuerySideModule&#45;&gt;FeatureQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M400,-187.11C400,-187.11 400,-205.99 400,-205.99"/>
<polygon fill="black" stroke="black" points="396.5,-205.99 400,-215.99 403.5,-205.99 396.5,-205.99"/>
</g>
<!-- FeatureService  -->
<g id="node7" class="node">
<title>FeatureService </title>
<polygon fill="#fb8072" stroke="black" points="299.96,-252 196.04,-252 196.04,-216 299.96,-216 299.96,-252"/>
<text text-anchor="middle" x="248" y="-229.8" font-family="Times,serif" font-size="14.00">FeatureService </text>
</g>
<!-- FeatureQuerySideModule&#45;&gt;FeatureService  -->
<g id="edge6" class="edge">
<title>FeatureQuerySideModule&#45;&gt;FeatureService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M319.1,-175C282.8,-175 248,-175 248,-175 248,-175 248,-205.98 248,-205.98"/>
<polygon fill="black" stroke="black" points="244.5,-205.98 248,-215.98 251.5,-205.98 244.5,-205.98"/>
</g>
<!-- FeatureQueryRepository -->
<g id="node8" class="node">
<title>FeatureQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="736" cy="-104" rx="106.5" ry="18"/>
<text text-anchor="middle" x="736" y="-99.8" font-family="Times,serif" font-size="14.00">FeatureQueryRepository</text>
</g>
<!-- FeatureQueryRepository&#45;&gt;FeatureQuerySideModule -->
<g id="edge7" class="edge">
<title>FeatureQueryRepository&#45;&gt;FeatureQuerySideModule</title>
<path fill="none" stroke="black" d="M643.72,-113.04C643.72,-130.9 643.72,-169 643.72,-169 643.72,-169 490.82,-169 490.82,-169"/>
<polygon fill="black" stroke="black" points="490.82,-165.5 480.82,-169 490.82,-172.5 490.82,-165.5"/>
</g>
<!-- FeatureService -->
<g id="node9" class="node">
<title>FeatureService</title>
<ellipse fill="#fdb462" stroke="black" cx="543" cy="-104" rx="68.89" ry="18"/>
<text text-anchor="middle" x="543" y="-99.8" font-family="Times,serif" font-size="14.00">FeatureService</text>
</g>
<!-- FeatureService&#45;&gt;FeatureQuerySideModule -->
<g id="edge8" class="edge">
<title>FeatureService&#45;&gt;FeatureQuerySideModule</title>
<path fill="none" stroke="black" d="M491.25,-116.15C491.25,-132.62 491.25,-160 491.25,-160 491.25,-160 490.2,-160 490.2,-160"/>
<polygon fill="black" stroke="black" points="490.78,-156.5 480.78,-160 490.78,-163.5 490.78,-156.5"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
<script src="../js/libs/svg-pan-zoom.min.js"></script>
<script src="../js/svg-pan-zoom.controls.js"></script>

<ul class="nav nav-tabs" role="tablist">
    <li  class="active" >
        <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
    </li>
    <li >
        <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">

        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/feature.queryside/module.ts</code>
        </p>




        <div class="container-fluid module">
            <div class="row">
                <div class="col-sm-3">
                    <h3>Providers<a href="https://angular.io/api/core/NgModule#providers" target="_blank"
                            title="Official documentation about module providers"><span class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../injectables/FeatureQueryRepository.html">FeatureQueryRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../injectables/FeatureService.html">FeatureService</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Controllers<a href="https://docs.nestjs.com/controllers" target="_blank" title="Official documentation about module controllers"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../controllers/FeatureController.html">FeatureController</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Imports<a href="https://angular.io/api/core/NgModule#imports" target="_blank" title="Official documentation about module imports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../modules/LoggerModule.html">LoggerModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/MsxQuerySideModule.html">MsxQuerySideModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/QueryDatabaseModule.html">QueryDatabaseModule</a>
                        </li>
                    </ul>
                </div>
                <div class="col-sm-3">
                    <h3>Exports<a href="https://angular.io/api/core/NgModule#exports" target="_blank" title="Official documentation about module exports"><span
                                class="icon ion-ios-information-circle-outline"></a></h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="../s/FeatureQueryRepository.html">FeatureQueryRepository</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../modules/FeatureQuerySideModule.html">FeatureQuerySideModule</a>
                        </li>
                        <li class="list-group-item">
                            <a href="../s/FeatureService.html">FeatureService</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { CqrsModule } from &#x27;@nestjs/cqrs&#x27;;
import { CommandHandlers } from &#x27;./commands/handlers&#x27;;
import { EventHandlers } from &#x27;./events&#x27;;
import { FeatureController } from &#x27;./controller&#x27;;
import { FeatureService } from &#x27;./service&#x27;;
import { FeatureQueryRepository } from &#x27;./repository/query.repository&#x27;;
import { QueryDatabaseModule } from &#x27;../database/query/query.database.module&#x27;;
import { QueryProviders } from &#x27;./providers/query.cqrs.providers&#x27;;
import { Module } from &#x27;@nestjs/common&#x27;;
import { MsxQuerySideModule } from &#x27;../msx.queryside/module&#x27;;
import { LoggerModule } from &#x27;../logger/logger.module&#x27;;

@Module({
  imports: [CqrsModule, QueryDatabaseModule, MsxQuerySideModule, LoggerModule],
  controllers: [FeatureController],
  providers: [
    FeatureService,

    FeatureQueryRepository,

    ...QueryProviders,
    ...CommandHandlers,
    ...EventHandlers,
  ],
  exports: [
    FeatureQueryRepository,
    FeatureQuerySideModule,
    FeatureService
  ]
})
export class FeatureQuerySideModule { }
</code></pre>
    </div>
</div>










                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'module';
            var COMPODOC_CURRENT_PAGE_URL = 'FeatureQuerySideModule.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
