<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-sts documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="./images/favicon.ico">
	      <link rel="stylesheet" href="./styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="./" class="navbar-brand">msx-sts documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content overview">
                   <div class="content-data">


<ol class="breadcrumb">
    <li>Overview</li>
</ol>
  
<div class="text-center module-graph-container">
    <div id="module-graph-svg">
        <?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.47.0 (20210316.0004)
 -->
<!-- Title: dependencies Pages: 1 -->
<svg width="13411pt" height="591pt"
 viewBox="0.00 0.00 13411.00 591.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 587)">
<title>dependencies</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-587 13407,-587 13407,4 -4,4"/>
<text text-anchor="start" x="6680.51" y="-42.4" font-family="Times-12" font-weight="bold" font-size="14.00">Legend</text>
<polygon fill="#ffffb3" stroke="transparent" points="6467.5,-10 6467.5,-30 6487.5,-30 6487.5,-10 6467.5,-10"/>
<text text-anchor="start" x="6491.13" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Declarations</text>
<polygon fill="#8dd3c7" stroke="transparent" points="6580.5,-10 6580.5,-30 6600.5,-30 6600.5,-10 6580.5,-10"/>
<text text-anchor="start" x="6604.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Module</text>
<polygon fill="#80b1d3" stroke="transparent" points="6666.5,-10 6666.5,-30 6686.5,-30 6686.5,-10 6666.5,-10"/>
<text text-anchor="start" x="6690.28" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Bootstrap</text>
<polygon fill="#fdb462" stroke="transparent" points="6763.5,-10 6763.5,-30 6783.5,-30 6783.5,-10 6763.5,-10"/>
<text text-anchor="start" x="6787.17" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Providers</text>
<polygon fill="#fb8072" stroke="transparent" points="6859.5,-10 6859.5,-30 6879.5,-30 6879.5,-10 6859.5,-10"/>
<text text-anchor="start" x="6883.23" y="-15.4" font-family="Times-12" font-size="14.00"> &#160;Exports</text>
<g id="clust1" class="cluster">
<title>cluster_ApplicationModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8326,-70 8326,-575 9650,-575 9650,-70 8326,-70"/>
</g>
<g id="clust3" class="cluster">
<title>cluster_ApplicationModule_imports</title>
<polygon fill="none" stroke="black" points="8334,-78 8334,-567 9508,-567 9508,-78 8334,-78"/>
</g>
<g id="clust7" class="cluster">
<title>cluster_AuthDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="2179,-135 2179,-203 3647,-203 3647,-135 2179,-135"/>
</g>
<g id="clust9" class="cluster">
<title>cluster_AuthDomainModule_imports</title>
<polygon fill="none" stroke="black" points="3467,-143 3467,-195 3639,-195 3639,-143 3467,-143"/>
</g>
<g id="clust10" class="cluster">
<title>cluster_AuthDomainModule_exports</title>
<polygon fill="none" stroke="black" points="2187,-143 2187,-195 2447,-195 2447,-143 2187,-143"/>
</g>
<g id="clust12" class="cluster">
<title>cluster_AuthDomainModule_providers</title>
<polygon fill="none" stroke="black" points="2455,-143 2455,-195 3459,-195 3459,-143 2455,-143"/>
</g>
<g id="clust13" class="cluster">
<title>cluster_AuthExportModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="1485,-135 1485,-355 2171,-355 2171,-135 1485,-135"/>
</g>
<g id="clust15" class="cluster">
<title>cluster_AuthExportModule_imports</title>
<polygon fill="none" stroke="black" points="2001,-143 2001,-195 2163,-195 2163,-143 2001,-143"/>
</g>
<g id="clust16" class="cluster">
<title>cluster_AuthExportModule_exports</title>
<polygon fill="none" stroke="black" points="1583,-295 1583,-347 2109,-347 2109,-295 1583,-295"/>
</g>
<g id="clust18" class="cluster">
<title>cluster_AuthExportModule_providers</title>
<polygon fill="none" stroke="black" points="1493,-143 1493,-195 1993,-195 1993,-143 1493,-143"/>
</g>
<g id="clust19" class="cluster">
<title>cluster_AuthQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="12708,-135 12708,-355 13195,-355 13195,-135 12708,-135"/>
</g>
<g id="clust22" class="cluster">
<title>cluster_AuthQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="12716,-295 12716,-347 13187,-347 13187,-295 12716,-295"/>
</g>
<g id="clust24" class="cluster">
<title>cluster_AuthQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="12716,-143 12716,-195 13110,-195 13110,-143 12716,-143"/>
</g>
<g id="clust25" class="cluster">
<title>cluster_CodeGenerateModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="8,-135 8,-355 522,-355 522,-135 8,-135"/>
</g>
<g id="clust28" class="cluster">
<title>cluster_CodeGenerateModule_exports</title>
<polygon fill="none" stroke="black" points="16,-295 16,-347 514,-347 514,-295 16,-295"/>
</g>
<g id="clust30" class="cluster">
<title>cluster_CodeGenerateModule_providers</title>
<polygon fill="none" stroke="black" points="72,-143 72,-195 514,-195 514,-143 72,-143"/>
</g>
<g id="clust31" class="cluster">
<title>cluster_ConfigModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="4655,-135 4655,-203 4907,-203 4907,-135 4655,-135"/>
</g>
<g id="clust34" class="cluster">
<title>cluster_ConfigModule_exports</title>
<polygon fill="none" stroke="black" points="4663,-143 4663,-195 4899,-195 4899,-143 4663,-143"/>
</g>
<g id="clust43" class="cluster">
<title>cluster_FeatureDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="7698,-211 7698,-279 8318,-279 8318,-211 7698,-211"/>
</g>
<g id="clust45" class="cluster">
<title>cluster_FeatureDomainModule_imports</title>
<polygon fill="none" stroke="black" points="8150,-219 8150,-271 8310,-271 8310,-219 8150,-219"/>
</g>
<g id="clust48" class="cluster">
<title>cluster_FeatureDomainModule_providers</title>
<polygon fill="none" stroke="black" points="7706,-219 7706,-271 8142,-271 8142,-219 7706,-219"/>
</g>
<g id="clust49" class="cluster">
<title>cluster_FeatureQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="10218,-287 10218,-355 10950,-355 10950,-287 10218,-287"/>
</g>
<g id="clust52" class="cluster">
<title>cluster_FeatureQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="10464,-295 10464,-347 10942,-347 10942,-295 10464,-295"/>
</g>
<g id="clust54" class="cluster">
<title>cluster_FeatureQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="10226,-295 10226,-347 10456,-347 10456,-295 10226,-295"/>
</g>
<g id="clust55" class="cluster">
<title>cluster_ListenerModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="990,-287 990,-355 1322,-355 1322,-287 990,-287"/>
</g>
<g id="clust57" class="cluster">
<title>cluster_ListenerModule_imports</title>
<polygon fill="none" stroke="black" points="998,-295 998,-347 1314,-347 1314,-295 998,-295"/>
</g>
<g id="clust61" class="cluster">
<title>cluster_LoggerModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="10942,-211 10942,-279 11416,-279 11416,-211 10942,-211"/>
</g>
<g id="clust64" class="cluster">
<title>cluster_LoggerModule_exports</title>
<polygon fill="none" stroke="black" points="11144,-219 11144,-271 11408,-271 11408,-219 11144,-219"/>
</g>
<g id="clust66" class="cluster">
<title>cluster_LoggerModule_providers</title>
<polygon fill="none" stroke="black" points="10950,-219 10950,-271 11136,-271 11136,-219 10950,-219"/>
</g>
<g id="clust67" class="cluster">
<title>cluster_MasterQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="965,-363 965,-431 1917,-431 1917,-363 965,-363"/>
</g>
<g id="clust70" class="cluster">
<title>cluster_MasterQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="1405,-371 1405,-423 1909,-423 1909,-371 1405,-371"/>
</g>
<g id="clust72" class="cluster">
<title>cluster_MasterQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="973,-371 973,-423 1397,-423 1397,-371 973,-371"/>
</g>
<g id="clust73" class="cluster">
<title>cluster_MgsSenderModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="4915,-135 4915,-203 7905,-203 7905,-135 4915,-135"/>
</g>
<g id="clust76" class="cluster">
<title>cluster_MgsSenderModule_exports</title>
<polygon fill="none" stroke="black" points="6597,-143 6597,-195 7897,-195 7897,-143 6597,-143"/>
</g>
<g id="clust78" class="cluster">
<title>cluster_MgsSenderModule_providers</title>
<polygon fill="none" stroke="black" points="4923,-143 4923,-195 6589,-195 6589,-143 4923,-143"/>
</g>
<g id="clust79" class="cluster">
<title>cluster_MsxDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="7913,-135 7913,-203 8318,-203 8318,-135 7913,-135"/>
</g>
<g id="clust84" class="cluster">
<title>cluster_MsxDomainModule_providers</title>
<polygon fill="none" stroke="black" points="7921,-143 7921,-195 8310,-195 8310,-143 7921,-143"/>
</g>
<g id="clust85" class="cluster">
<title>cluster_MsxQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="9658,-287 9658,-355 10210,-355 10210,-287 9658,-287"/>
</g>
<g id="clust88" class="cluster">
<title>cluster_MsxQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="9880,-295 9880,-347 10202,-347 10202,-295 9880,-295"/>
</g>
<g id="clust90" class="cluster">
<title>cluster_MsxQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="9666,-295 9666,-347 9872,-347 9872,-295 9666,-295"/>
</g>
<g id="clust91" class="cluster">
<title>cluster_PatchDataModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="13203,-287 13203,-499 13395,-499 13395,-287 13203,-287"/>
</g>
<g id="clust94" class="cluster">
<title>cluster_PatchDataModule_exports</title>
<polygon fill="none" stroke="black" points="13211,-439 13211,-491 13347,-491 13347,-439 13211,-439"/>
</g>
<g id="clust96" class="cluster">
<title>cluster_PatchDataModule_providers</title>
<polygon fill="none" stroke="black" points="13211,-295 13211,-347 13387,-347 13387,-295 13211,-295"/>
</g>
<g id="clust97" class="cluster">
<title>cluster_PermissionDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="3655,-135 3655,-203 4163,-203 4163,-135 3655,-135"/>
</g>
<g id="clust102" class="cluster">
<title>cluster_PermissionDomainModule_providers</title>
<polygon fill="none" stroke="black" points="3663,-143 3663,-195 4155,-195 4155,-143 3663,-143"/>
</g>
<g id="clust103" class="cluster">
<title>cluster_PermissionQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="10958,-287 10958,-355 11636,-355 11636,-287 10958,-287"/>
</g>
<g id="clust106" class="cluster">
<title>cluster_PermissionQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="11230,-295 11230,-347 11628,-347 11628,-295 11230,-295"/>
</g>
<g id="clust108" class="cluster">
<title>cluster_PermissionQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="10966,-295 10966,-347 11222,-347 11222,-295 10966,-295"/>
</g>
<g id="clust115" class="cluster">
<title>cluster_RedisCacheModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="11644,-287 11644,-355 12000,-355 12000,-287 11644,-287"/>
</g>
<g id="clust118" class="cluster">
<title>cluster_RedisCacheModule_exports</title>
<polygon fill="none" stroke="black" points="11848,-295 11848,-347 11992,-347 11992,-295 11848,-295"/>
</g>
<g id="clust120" class="cluster">
<title>cluster_RedisCacheModule_providers</title>
<polygon fill="none" stroke="black" points="11652,-295 11652,-347 11840,-347 11840,-295 11652,-295"/>
</g>
<g id="clust121" class="cluster">
<title>cluster_RoleDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="4171,-135 4171,-203 4581,-203 4581,-135 4171,-135"/>
</g>
<g id="clust126" class="cluster">
<title>cluster_RoleDomainModule_providers</title>
<polygon fill="none" stroke="black" points="4179,-143 4179,-195 4573,-195 4573,-143 4179,-143"/>
</g>
<g id="clust127" class="cluster">
<title>cluster_RoleQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="12008,-287 12008,-355 12672,-355 12672,-287 12008,-287"/>
</g>
<g id="clust130" class="cluster">
<title>cluster_RoleQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="12232,-295 12232,-347 12664,-347 12664,-295 12232,-295"/>
</g>
<g id="clust132" class="cluster">
<title>cluster_RoleQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="12016,-295 12016,-347 12224,-347 12224,-295 12016,-295"/>
</g>
<g id="clust133" class="cluster">
<title>cluster_UserDomainModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="9658,-507 9658,-575 10440,-575 10440,-507 9658,-507"/>
</g>
<g id="clust136" class="cluster">
<title>cluster_UserDomainModule_exports</title>
<polygon fill="none" stroke="black" points="10130,-515 10130,-567 10432,-567 10432,-515 10130,-515"/>
</g>
<g id="clust138" class="cluster">
<title>cluster_UserDomainModule_providers</title>
<polygon fill="none" stroke="black" points="9666,-515 9666,-567 10122,-567 10122,-515 9666,-515"/>
</g>
<g id="clust139" class="cluster">
<title>cluster_UserExportModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="748,-211 748,-279 1477,-279 1477,-211 748,-211"/>
</g>
<g id="clust142" class="cluster">
<title>cluster_UserExportModule_exports</title>
<polygon fill="none" stroke="black" points="1163,-219 1163,-271 1469,-271 1469,-219 1163,-219"/>
</g>
<g id="clust144" class="cluster">
<title>cluster_UserExportModule_providers</title>
<polygon fill="none" stroke="black" points="756,-219 756,-271 1155,-271 1155,-219 756,-219"/>
</g>
<g id="clust145" class="cluster">
<title>cluster_UserQuerySideModule</title>
<polygon fill="none" stroke="black" stroke-dasharray="1,5" points="9658,-363 9658,-431 10456,-431 10456,-363 9658,-363"/>
</g>
<g id="clust148" class="cluster">
<title>cluster_UserQuerySideModule_exports</title>
<polygon fill="none" stroke="black" points="10016,-371 10016,-423 10448,-423 10448,-371 10016,-371"/>
</g>
<g id="clust150" class="cluster">
<title>cluster_UserQuerySideModule_providers</title>
<polygon fill="none" stroke="black" points="9666,-371 9666,-423 10008,-423 10008,-371 9666,-371"/>
</g>
<!-- AuthDomainModule -->
<g id="node1" class="node">
<title>AuthDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="8474.15,-415 8471.15,-419 8450.15,-419 8447.15,-415 8341.85,-415 8341.85,-379 8474.15,-379 8474.15,-415"/>
<text text-anchor="middle" x="8408" y="-392.8" font-family="Times,serif" font-size="14.00">AuthDomainModule</text>
</g>
<!-- ListenerModule -->
<g id="node5" class="node">
<title>ListenerModule</title>
<polygon fill="#8dd3c7" stroke="black" points="9010.92,-559 9007.92,-563 8986.92,-563 8983.92,-559 8905.08,-559 8905.08,-523 9010.92,-523 9010.92,-559"/>
<text text-anchor="middle" x="8958" y="-536.8" font-family="Times,serif" font-size="14.00">ListenerModule</text>
</g>
<!-- AuthDomainModule&#45;&gt;ListenerModule -->
<g id="edge70" class="edge">
<title>AuthDomainModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M8474.17,-411C8610.13,-411 8908.25,-411 8908.25,-411 8908.25,-411 8908.25,-512.92 8908.25,-512.92"/>
<polygon fill="black" stroke="black" points="8904.75,-512.92 8908.25,-522.92 8911.75,-512.92 8904.75,-512.92"/>
</g>
<!-- UserDomainModule -->
<g id="node14" class="node">
<title>UserDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="9066.58,-483 9063.58,-487 9042.58,-487 9039.58,-483 8935.42,-483 8935.42,-447 9066.58,-447 9066.58,-483"/>
<text text-anchor="middle" x="9001" y="-460.8" font-family="Times,serif" font-size="14.00">UserDomainModule</text>
</g>
<!-- AuthDomainModule&#45;&gt;UserDomainModule -->
<g id="edge172" class="edge">
<title>AuthDomainModule&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M8474.29,-406C8618.21,-406 8946.43,-406 8946.43,-406 8946.43,-406 8946.43,-436.98 8946.43,-436.98"/>
<polygon fill="black" stroke="black" points="8942.93,-436.98 8946.43,-446.98 8949.93,-436.98 8942.93,-436.98"/>
</g>
<!-- ApplicationModule -->
<g id="node16" class="node">
<title>ApplicationModule</title>
<polygon fill="#8dd3c7" stroke="black" points="9641.65,-559 9638.65,-563 9617.65,-563 9614.65,-559 9516.35,-559 9516.35,-523 9641.65,-523 9641.65,-559"/>
<text text-anchor="middle" x="9579" y="-536.8" font-family="Times,serif" font-size="14.00">ApplicationModule</text>
</g>
<!-- AuthDomainModule&#45;&gt;ApplicationModule -->
<g id="edge1" class="edge">
<title>AuthDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M8474.27,-402C8714.78,-402 9527.6,-402 9527.6,-402 9527.6,-402 9527.6,-512.83 9527.6,-512.83"/>
<polygon fill="black" stroke="black" points="9524.1,-512.83 9527.6,-522.83 9531.1,-512.83 9524.1,-512.83"/>
</g>
<!-- AuthDomainModule  -->
<g id="node18" class="node">
<title>AuthDomainModule </title>
<polygon fill="#fb8072" stroke="black" points="2439.15,-187 2302.85,-187 2302.85,-151 2439.15,-151 2439.15,-187"/>
<text text-anchor="middle" x="2371" y="-164.8" font-family="Times,serif" font-size="14.00">AuthDomainModule </text>
</g>
<!-- AuthDomainModule&#45;&gt;AuthDomainModule  -->
<g id="edge22" class="edge">
<title>AuthDomainModule&#45;&gt;AuthDomainModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8341.75,-409C7685.25,-409 2371,-409 2371,-409 2371,-409 2371,-197.04 2371,-197.04"/>
<polygon fill="black" stroke="black" points="2374.5,-197.04 2371,-187.04 2367.5,-197.04 2374.5,-197.04"/>
</g>
<!-- AuthService  -->
<g id="node19" class="node">
<title>AuthService </title>
<polygon fill="#fb8072" stroke="black" points="2284.98,-187 2195.02,-187 2195.02,-151 2284.98,-151 2284.98,-187"/>
<text text-anchor="middle" x="2240" y="-164.8" font-family="Times,serif" font-size="14.00">AuthService </text>
</g>
<!-- AuthDomainModule&#45;&gt;AuthService  -->
<g id="edge23" class="edge">
<title>AuthDomainModule&#45;&gt;AuthService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8341.61,-412C7676.63,-412 2240,-412 2240,-412 2240,-412 2240,-197.26 2240,-197.26"/>
<polygon fill="black" stroke="black" points="2243.5,-197.26 2240,-187.26 2236.5,-197.26 2243.5,-197.26"/>
</g>
<!-- ConfigModule -->
<g id="node2" class="node">
<title>ConfigModule</title>
<polygon fill="#8dd3c7" stroke="black" points="8582.44,-122 8579.44,-126 8558.44,-126 8555.44,-122 8483.56,-122 8483.56,-86 8582.44,-86 8582.44,-122"/>
<text text-anchor="middle" x="8533" y="-99.8" font-family="Times,serif" font-size="14.00">ConfigModule</text>
</g>
<!-- ConfigModule&#45;&gt;AuthDomainModule -->
<g id="edge17" class="edge">
<title>ConfigModule&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M8515.64,-122.2C8515.64,-184.88 8515.64,-388 8515.64,-388 8515.64,-388 8484.33,-388 8484.33,-388"/>
<polygon fill="black" stroke="black" points="8484.33,-384.5 8474.33,-388 8484.33,-391.5 8484.33,-384.5"/>
</g>
<!-- ConfigModule&#45;&gt;ListenerModule -->
<g id="edge71" class="edge">
<title>ConfigModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M8519.25,-122.22C8519.25,-204.93 8519.25,-541 8519.25,-541 8519.25,-541 8895.08,-541 8895.08,-541"/>
<polygon fill="black" stroke="black" points="8895.08,-544.5 8905.08,-541 8895.08,-537.5 8895.08,-544.5"/>
</g>
<!-- LoggerModule -->
<g id="node6" class="node">
<title>LoggerModule</title>
<polygon fill="#8dd3c7" stroke="black" points="8672.98,-187 8669.98,-191 8648.98,-191 8645.98,-187 8573.02,-187 8573.02,-151 8672.98,-151 8672.98,-187"/>
<text text-anchor="middle" x="8623" y="-164.8" font-family="Times,serif" font-size="14.00">LoggerModule</text>
</g>
<!-- ConfigModule&#45;&gt;LoggerModule -->
<g id="edge81" class="edge">
<title>ConfigModule&#45;&gt;LoggerModule</title>
<path fill="none" stroke="black" d="M8579.15,-122.11C8579.15,-122.11 8579.15,-140.99 8579.15,-140.99"/>
<polygon fill="black" stroke="black" points="8575.65,-140.99 8579.15,-150.99 8582.65,-140.99 8575.65,-140.99"/>
</g>
<!-- ConfigModule&#45;&gt;UserDomainModule -->
<g id="edge174" class="edge">
<title>ConfigModule&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M8582.47,-113C8698.68,-113 8979.33,-113 8979.33,-113 8979.33,-113 8979.33,-436.57 8979.33,-436.57"/>
<polygon fill="black" stroke="black" points="8975.83,-436.57 8979.33,-446.57 8982.83,-436.57 8975.83,-436.57"/>
</g>
<!-- ConfigModule&#45;&gt;ApplicationModule -->
<g id="edge2" class="edge">
<title>ConfigModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M8582.28,-104C8795.82,-104 9630.4,-104 9630.4,-104 9630.4,-104 9630.4,-512.78 9630.4,-512.78"/>
<polygon fill="black" stroke="black" points="9626.9,-512.78 9630.4,-522.78 9633.9,-512.78 9626.9,-512.78"/>
</g>
<!-- AuthQuerySideModule -->
<g id="node33" class="node">
<title>AuthQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="13028.92,-263 13025.92,-267 13004.92,-267 13001.92,-263 12881.08,-263 12881.08,-227 13028.92,-227 13028.92,-263"/>
<text text-anchor="middle" x="12955" y="-240.8" font-family="Times,serif" font-size="14.00">AuthQuerySideModule</text>
</g>
<!-- ConfigModule&#45;&gt;AuthQuerySideModule -->
<g id="edge37" class="edge">
<title>ConfigModule&#45;&gt;AuthQuerySideModule</title>
<path fill="none" stroke="black" d="M8582.44,-95C9064.09,-95 12902.24,-95 12902.24,-95 12902.24,-95 12902.24,-216.91 12902.24,-216.91"/>
<polygon fill="black" stroke="black" points="12898.74,-216.91 12902.24,-226.91 12905.74,-216.91 12898.74,-216.91"/>
</g>
<!-- ConfigModule  -->
<g id="node45" class="node">
<title>ConfigModule </title>
<polygon fill="#fb8072" stroke="black" points="4890.94,-187 4789.06,-187 4789.06,-151 4890.94,-151 4890.94,-187"/>
<text text-anchor="middle" x="4840" y="-164.8" font-family="Times,serif" font-size="14.00">ConfigModule </text>
</g>
<!-- ConfigModule&#45;&gt;ConfigModule  -->
<g id="edge52" class="edge">
<title>ConfigModule&#45;&gt;ConfigModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8494.35,-122.11C8494.35,-131.9 8494.35,-142 8494.35,-142 8494.35,-142 4840,-142 4840,-142 4840,-142 4840,-142.88 4840,-142.88"/>
<polygon fill="black" stroke="black" points="4836.5,-140.84 4840,-150.84 4843.5,-140.84 4836.5,-140.84"/>
</g>
<!-- ConfigService  -->
<g id="node46" class="node">
<title>ConfigService </title>
<polygon fill="#fb8072" stroke="black" points="4771.37,-187 4670.63,-187 4670.63,-151 4771.37,-151 4771.37,-187"/>
<text text-anchor="middle" x="4721" y="-164.8" font-family="Times,serif" font-size="14.00">ConfigService </text>
</g>
<!-- ConfigModule&#45;&gt;ConfigService  -->
<g id="edge53" class="edge">
<title>ConfigModule&#45;&gt;ConfigService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8490.83,-122.04C8490.83,-129.75 8490.83,-137 8490.83,-137 8490.83,-137 4721,-137 4721,-137 4721,-137 4721,-140.76 4721,-140.76"/>
<polygon fill="black" stroke="black" points="4717.5,-140.76 4721,-150.76 4724.5,-140.76 4717.5,-140.76"/>
</g>
<!-- MasterQuerySideModule -->
<g id="node54" class="node">
<title>MasterQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1164.29,-339 1161.29,-343 1140.29,-343 1137.29,-339 1005.71,-339 1005.71,-303 1164.29,-303 1164.29,-339"/>
<text text-anchor="middle" x="1085" y="-316.8" font-family="Times,serif" font-size="14.00">MasterQuerySideModule</text>
</g>
<!-- ConfigModule&#45;&gt;MasterQuerySideModule -->
<g id="edge86" class="edge">
<title>ConfigModule&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M8487.3,-122.12C8487.3,-124.97 8487.3,-127 8487.3,-127 8487.3,-127 1152.38,-127 1152.38,-127 1152.38,-127 1152.38,-292.75 1152.38,-292.75"/>
<polygon fill="black" stroke="black" points="1148.88,-292.75 1152.38,-302.75 1155.88,-292.75 1148.88,-292.75"/>
</g>
<!-- UserExportModule -->
<g id="node55" class="node">
<title>UserExportModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1306.08,-339 1303.08,-343 1282.08,-343 1279.08,-339 1181.92,-339 1181.92,-303 1306.08,-303 1306.08,-339"/>
<text text-anchor="middle" x="1244" y="-316.8" font-family="Times,serif" font-size="14.00">UserExportModule</text>
</g>
<!-- ConfigModule&#45;&gt;UserExportModule -->
<g id="edge189" class="edge">
<title>ConfigModule&#45;&gt;UserExportModule</title>
<path fill="none" stroke="black" d="M8512.04,-122.28C8512.04,-178.01 8512.04,-342 8512.04,-342 8512.04,-342 1286.25,-342 1286.25,-342 1286.25,-342 1286.25,-341.71 1286.25,-341.71"/>
<polygon fill="black" stroke="black" points="1289.75,-349.08 1286.25,-339.08 1282.75,-349.08 1289.75,-349.08"/>
</g>
<!-- FeatureDomainModule -->
<g id="node3" class="node">
<title>FeatureDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="8822.13,-339 8819.13,-343 8798.13,-343 8795.13,-339 8675.87,-339 8675.87,-303 8822.13,-303 8822.13,-339"/>
<text text-anchor="middle" x="8749" y="-316.8" font-family="Times,serif" font-size="14.00">FeatureDomainModule</text>
</g>
<!-- FeatureDomainModule&#45;&gt;ApplicationModule -->
<g id="edge3" class="edge">
<title>FeatureDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M8822.17,-325C8917.8,-325 9073.25,-325 9073.25,-325 9073.25,-325 9073.25,-539 9073.25,-539 9073.25,-539 9506.22,-539 9506.22,-539"/>
<polygon fill="black" stroke="black" points="9506.22,-542.5 9516.22,-539 9506.22,-535.5 9506.22,-542.5"/>
</g>
<!-- FeatureQuerySideModule -->
<g id="node4" class="node">
<title>FeatureQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="9151.9,-263 9148.9,-267 9127.9,-267 9124.9,-263 8990.1,-263 8990.1,-227 9151.9,-227 9151.9,-263"/>
<text text-anchor="middle" x="9071" y="-240.8" font-family="Times,serif" font-size="14.00">FeatureQuerySideModule</text>
</g>
<!-- FeatureQuerySideModule&#45;&gt;FeatureDomainModule -->
<g id="edge55" class="edge">
<title>FeatureQuerySideModule&#45;&gt;FeatureDomainModule</title>
<path fill="none" stroke="black" d="M9041.13,-263.03C9041.13,-282.58 9041.13,-311 9041.13,-311 9041.13,-311 8832.34,-311 8832.34,-311"/>
<polygon fill="black" stroke="black" points="8832.34,-307.5 8822.34,-311 8832.34,-314.5 8832.34,-307.5"/>
</g>
<!-- FeatureQuerySideModule&#45;&gt;ListenerModule -->
<g id="edge72" class="edge">
<title>FeatureQuerySideModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M9079.95,-263.19C9079.95,-327.89 9079.95,-543 9079.95,-543 9079.95,-543 9020.78,-543 9020.78,-543"/>
<polygon fill="black" stroke="black" points="9020.78,-539.5 9010.78,-543 9020.78,-546.5 9020.78,-539.5"/>
</g>
<!-- PermissionDomainModule -->
<g id="node9" class="node">
<title>PermissionDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="8508.37,-339 8505.37,-343 8484.37,-343 8481.37,-339 8341.63,-339 8341.63,-303 8508.37,-303 8508.37,-339"/>
<text text-anchor="middle" x="8425" y="-316.8" font-family="Times,serif" font-size="14.00">PermissionDomainModule</text>
</g>
<!-- FeatureQuerySideModule&#45;&gt;PermissionDomainModule -->
<g id="edge139" class="edge">
<title>FeatureQuerySideModule&#45;&gt;PermissionDomainModule</title>
<path fill="none" stroke="black" d="M9028.42,-263.05C9028.42,-277.37 9028.42,-295 9028.42,-295 9028.42,-295 8504.91,-295 8504.91,-295 8504.91,-295 8504.91,-295.79 8504.91,-295.79"/>
<polygon fill="black" stroke="black" points="8501.41,-292.94 8504.91,-302.94 8508.41,-292.94 8501.41,-292.94"/>
</g>
<!-- UserQuerySideModule -->
<g id="node15" class="node">
<title>UserQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="9245.85,-339 9242.85,-343 9221.85,-343 9218.85,-339 9100.15,-339 9100.15,-303 9245.85,-303 9245.85,-339"/>
<text text-anchor="middle" x="9173" y="-316.8" font-family="Times,serif" font-size="14.00">UserQuerySideModule</text>
</g>
<!-- FeatureQuerySideModule&#45;&gt;UserQuerySideModule -->
<g id="edge198" class="edge">
<title>FeatureQuerySideModule&#45;&gt;UserQuerySideModule</title>
<path fill="none" stroke="black" d="M9119.44,-263.01C9119.44,-263.01 9119.44,-292.85 9119.44,-292.85"/>
<polygon fill="black" stroke="black" points="9115.94,-292.85 9119.44,-302.85 9122.94,-292.85 9115.94,-292.85"/>
</g>
<!-- FeatureQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge4" class="edge">
<title>FeatureQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M9086.66,-263.14C9086.66,-326.63 9086.66,-535 9086.66,-535 9086.66,-535 9506.14,-535 9506.14,-535"/>
<polygon fill="black" stroke="black" points="9506.15,-538.5 9516.14,-535 9506.14,-531.5 9506.15,-538.5"/>
</g>
<!-- FeatureQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge5" class="edge">
<title>FeatureQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M9093.37,-263.33C9093.37,-326.45 9093.37,-531 9093.37,-531 9093.37,-531 9506.01,-531 9506.01,-531"/>
<polygon fill="black" stroke="black" points="9506.01,-534.5 9516.01,-531 9506.01,-527.5 9506.01,-534.5"/>
</g>
<!-- FeatureQueryRepository  -->
<g id="node50" class="node">
<title>FeatureQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="10933.9,-339 10776.1,-339 10776.1,-303 10933.9,-303 10933.9,-339"/>
<text text-anchor="middle" x="10855" y="-316.8" font-family="Times,serif" font-size="14.00">FeatureQueryRepository </text>
</g>
<!-- FeatureQuerySideModule&#45;&gt;FeatureQueryRepository  -->
<g id="edge65" class="edge">
<title>FeatureQuerySideModule&#45;&gt;FeatureQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9138.79,-263.21C9138.79,-268.55 9138.79,-273 9138.79,-273 9138.79,-273 10855,-273 10855,-273 10855,-273 10855,-292.85 10855,-292.85"/>
<polygon fill="black" stroke="black" points="10851.5,-292.85 10855,-302.85 10858.5,-292.85 10851.5,-292.85"/>
</g>
<!-- FeatureQuerySideModule  -->
<g id="node51" class="node">
<title>FeatureQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="10758.4,-339 10593.6,-339 10593.6,-303 10758.4,-303 10758.4,-339"/>
<text text-anchor="middle" x="10676" y="-316.8" font-family="Times,serif" font-size="14.00">FeatureQuerySideModule </text>
</g>
<!-- FeatureQuerySideModule&#45;&gt;FeatureQuerySideModule  -->
<g id="edge66" class="edge">
<title>FeatureQuerySideModule&#45;&gt;FeatureQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9132.34,-263.2C9132.34,-269.03 9132.34,-274 9132.34,-274 9132.34,-274 10676,-274 10676,-274 10676,-274 10676,-292.96 10676,-292.96"/>
<polygon fill="black" stroke="black" points="10672.5,-292.96 10676,-302.96 10679.5,-292.96 10672.5,-292.96"/>
</g>
<!-- FeatureService  -->
<g id="node52" class="node">
<title>FeatureService </title>
<polygon fill="#fb8072" stroke="black" points="10575.96,-339 10472.04,-339 10472.04,-303 10575.96,-303 10575.96,-339"/>
<text text-anchor="middle" x="10524" y="-316.8" font-family="Times,serif" font-size="14.00">FeatureService </text>
</g>
<!-- FeatureQuerySideModule&#45;&gt;FeatureService  -->
<g id="edge67" class="edge">
<title>FeatureQuerySideModule&#45;&gt;FeatureService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9125.89,-263.14C9125.89,-269.46 9125.89,-275 9125.89,-275 9125.89,-275 10524,-275 10524,-275 10524,-275 10524,-292.82 10524,-292.82"/>
<polygon fill="black" stroke="black" points="10520.5,-292.82 10524,-302.82 10527.5,-292.82 10520.5,-292.82"/>
</g>
<!-- ListenerModule&#45;&gt;ApplicationModule -->
<g id="edge6" class="edge">
<title>ListenerModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M9010.95,-555C9010.95,-555 9506.15,-555 9506.15,-555"/>
<polygon fill="black" stroke="black" points="9506.15,-558.5 9516.15,-555 9506.15,-551.5 9506.15,-558.5"/>
</g>
<!-- LoggerModule&#45;&gt;AuthDomainModule -->
<g id="edge19" class="edge">
<title>LoggerModule&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M8572.92,-178C8547.89,-178 8522.85,-178 8522.85,-178 8522.85,-178 8522.85,-393 8522.85,-393 8522.85,-393 8484.48,-393 8484.48,-393"/>
<polygon fill="black" stroke="black" points="8484.48,-389.5 8474.48,-393 8484.48,-396.5 8484.48,-389.5"/>
</g>
<!-- LoggerModule&#45;&gt;FeatureDomainModule -->
<g id="edge56" class="edge">
<title>LoggerModule&#45;&gt;FeatureDomainModule</title>
<path fill="none" stroke="black" d="M8673.04,-184C8718.05,-184 8777.14,-184 8777.14,-184 8777.14,-184 8777.14,-292.81 8777.14,-292.81"/>
<polygon fill="black" stroke="black" points="8773.64,-292.81 8777.14,-302.81 8780.64,-292.81 8773.64,-292.81"/>
</g>
<!-- LoggerModule&#45;&gt;FeatureQuerySideModule -->
<g id="edge62" class="edge">
<title>LoggerModule&#45;&gt;FeatureQuerySideModule</title>
<path fill="none" stroke="black" d="M8673.37,-175C8795.59,-175 9097.9,-175 9097.9,-175 9097.9,-175 9097.9,-216.97 9097.9,-216.97"/>
<polygon fill="black" stroke="black" points="9094.4,-216.97 9097.9,-226.97 9101.4,-216.97 9094.4,-216.97"/>
</g>
<!-- LoggerModule&#45;&gt;ListenerModule -->
<g id="edge73" class="edge">
<title>LoggerModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M8630.33,-187.15C8630.33,-221.36 8630.33,-292 8630.33,-292 8630.33,-292 8917.13,-292 8917.13,-292 8917.13,-292 8917.13,-512.69 8917.13,-512.69"/>
<polygon fill="black" stroke="black" points="8913.63,-512.69 8917.13,-522.69 8920.63,-512.69 8913.63,-512.69"/>
</g>
<!-- MsxDomainModule -->
<g id="node8" class="node">
<title>MsxDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="9499.54,-263 9496.54,-267 9475.54,-267 9472.54,-263 9370.46,-263 9370.46,-227 9499.54,-227 9499.54,-263"/>
<text text-anchor="middle" x="9435" y="-240.8" font-family="Times,serif" font-size="14.00">MsxDomainModule</text>
</g>
<!-- LoggerModule&#45;&gt;MsxDomainModule -->
<g id="edge119" class="edge">
<title>LoggerModule&#45;&gt;MsxDomainModule</title>
<path fill="none" stroke="black" d="M8673.17,-166C8856.74,-166 9481.26,-166 9481.26,-166 9481.26,-166 9481.26,-216.68 9481.26,-216.68"/>
<polygon fill="black" stroke="black" points="9477.76,-216.68 9481.26,-226.68 9484.76,-216.68 9477.76,-216.68"/>
</g>
<!-- LoggerModule&#45;&gt;PermissionDomainModule -->
<g id="edge140" class="edge">
<title>LoggerModule&#45;&gt;PermissionDomainModule</title>
<path fill="none" stroke="black" d="M8572.83,-169C8538.01,-169 8497.87,-169 8497.87,-169 8497.87,-169 8497.87,-292.97 8497.87,-292.97"/>
<polygon fill="black" stroke="black" points="8494.37,-292.97 8497.87,-302.97 8501.37,-292.97 8494.37,-292.97"/>
</g>
<!-- PermissionQuerySideModule -->
<g id="node10" class="node">
<title>PermissionQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="9351.64,-263 9348.64,-267 9327.64,-267 9324.64,-263 9170.36,-263 9170.36,-227 9351.64,-227 9351.64,-263"/>
<text text-anchor="middle" x="9261" y="-240.8" font-family="Times,serif" font-size="14.00">PermissionQuerySideModule</text>
</g>
<!-- LoggerModule&#45;&gt;PermissionQuerySideModule -->
<g id="edge146" class="edge">
<title>LoggerModule&#45;&gt;PermissionQuerySideModule</title>
<path fill="none" stroke="black" d="M8673.24,-169C8833.41,-169 9321.55,-169 9321.55,-169 9321.55,-169 9321.55,-216.99 9321.55,-216.99"/>
<polygon fill="black" stroke="black" points="9318.05,-216.99 9321.55,-226.99 9325.05,-216.99 9318.05,-216.99"/>
</g>
<!-- RoleDomainModule -->
<g id="node12" class="node">
<title>RoleDomainModule</title>
<polygon fill="#8dd3c7" stroke="black" points="8657.59,-339 8654.59,-343 8633.59,-343 8630.59,-339 8526.41,-339 8526.41,-303 8657.59,-303 8657.59,-339"/>
<text text-anchor="middle" x="8592" y="-316.8" font-family="Times,serif" font-size="14.00">RoleDomainModule</text>
</g>
<!-- LoggerModule&#45;&gt;RoleDomainModule -->
<g id="edge157" class="edge">
<title>LoggerModule&#45;&gt;RoleDomainModule</title>
<path fill="none" stroke="black" d="M8618.86,-187.03C8618.86,-187.03 8618.86,-292.99 8618.86,-292.99"/>
<polygon fill="black" stroke="black" points="8615.36,-292.99 8618.86,-302.99 8622.36,-292.99 8615.36,-292.99"/>
</g>
<!-- RoleQuerySideModule -->
<g id="node13" class="node">
<title>RoleQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="8931.86,-263 8928.86,-267 8907.86,-267 8904.86,-263 8786.14,-263 8786.14,-227 8931.86,-227 8931.86,-263"/>
<text text-anchor="middle" x="8859" y="-240.8" font-family="Times,serif" font-size="14.00">RoleQuerySideModule</text>
</g>
<!-- LoggerModule&#45;&gt;RoleQuerySideModule -->
<g id="edge164" class="edge">
<title>LoggerModule&#45;&gt;RoleQuerySideModule</title>
<path fill="none" stroke="black" d="M8673.02,-181C8753.48,-181 8902.76,-181 8902.76,-181 8902.76,-181 8902.76,-216.83 8902.76,-216.83"/>
<polygon fill="black" stroke="black" points="8899.26,-216.83 8902.76,-226.83 8906.26,-216.83 8899.26,-216.83"/>
</g>
<!-- LoggerModule&#45;&gt;UserDomainModule -->
<g id="edge176" class="edge">
<title>LoggerModule&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M8673.22,-178C8769.01,-178 8968.37,-178 8968.37,-178 8968.37,-178 8968.37,-436.61 8968.37,-436.61"/>
<polygon fill="black" stroke="black" points="8964.87,-436.61 8968.37,-446.61 8971.87,-436.61 8964.87,-436.61"/>
</g>
<!-- LoggerModule&#45;&gt;UserQuerySideModule -->
<g id="edge199" class="edge">
<title>LoggerModule&#45;&gt;UserQuerySideModule</title>
<path fill="none" stroke="black" d="M8673.12,-172C8806.67,-172 9160.94,-172 9160.94,-172 9160.94,-172 9160.94,-292.73 9160.94,-292.73"/>
<polygon fill="black" stroke="black" points="9157.44,-292.73 9160.94,-302.73 9164.44,-292.73 9157.44,-292.73"/>
</g>
<!-- LoggerModule&#45;&gt;ApplicationModule -->
<g id="edge7" class="edge">
<title>LoggerModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M8673.07,-163C8875.04,-163 9618.98,-163 9618.98,-163 9618.98,-163 9618.98,-512.77 9618.98,-512.77"/>
<polygon fill="black" stroke="black" points="9615.48,-512.77 9618.98,-522.77 9622.48,-512.77 9615.48,-512.77"/>
</g>
<!-- AuthExportModule -->
<g id="node27" class="node">
<title>AuthExportModule</title>
<polygon fill="#8dd3c7" stroke="black" points="1924.65,-263 1921.65,-267 1900.65,-267 1897.65,-263 1799.35,-263 1799.35,-227 1924.65,-227 1924.65,-263"/>
<text text-anchor="middle" x="1862" y="-240.8" font-family="Times,serif" font-size="14.00">AuthExportModule</text>
</g>
<!-- LoggerModule&#45;&gt;AuthExportModule -->
<g id="edge30" class="edge">
<title>LoggerModule&#45;&gt;AuthExportModule</title>
<path fill="none" stroke="black" d="M8595.94,-187.14C8595.94,-193.46 8595.94,-199 8595.94,-199 8595.94,-199 1882.94,-199 1882.94,-199 1882.94,-199 1882.94,-216.82 1882.94,-216.82"/>
<polygon fill="black" stroke="black" points="1879.44,-216.82 1882.94,-226.82 1886.44,-216.82 1879.44,-216.82"/>
</g>
<!-- LoggerModule&#45;&gt;AuthQuerySideModule -->
<g id="edge38" class="edge">
<title>LoggerModule&#45;&gt;AuthQuerySideModule</title>
<path fill="none" stroke="black" d="M8652.19,-187.17C8652.19,-189.45 8652.19,-191 8652.19,-191 8652.19,-191 12885.86,-191 12885.86,-191 12885.86,-191 12885.86,-216.72 12885.86,-216.72"/>
<polygon fill="black" stroke="black" points="12882.36,-216.72 12885.86,-226.72 12889.36,-216.72 12882.36,-216.72"/>
</g>
<!-- MsxQuerySideModule -->
<g id="node47" class="node">
<title>MsxQuerySideModule</title>
<polygon fill="#8dd3c7" stroke="black" points="8302.31,-263 8299.31,-267 8278.31,-267 8275.31,-263 8157.69,-263 8157.69,-227 8302.31,-227 8302.31,-263"/>
<text text-anchor="middle" x="8230" y="-240.8" font-family="Times,serif" font-size="14.00">MsxQuerySideModule</text>
</g>
<!-- LoggerModule&#45;&gt;MsxQuerySideModule -->
<g id="edge125" class="edge">
<title>LoggerModule&#45;&gt;MsxQuerySideModule</title>
<path fill="none" stroke="black" d="M8607.4,-187.2C8607.4,-211.36 8607.4,-251 8607.4,-251 8607.4,-251 8312.32,-251 8312.32,-251"/>
<polygon fill="black" stroke="black" points="8312.32,-247.5 8302.32,-251 8312.32,-254.5 8312.32,-247.5"/>
</g>
<!-- LoggerModule&#45;&gt;MasterQuerySideModule -->
<g id="edge87" class="edge">
<title>LoggerModule&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M8576.08,-150.93C8576.08,-141.54 8576.08,-132 8576.08,-132 8576.08,-132 1155.32,-132 1155.32,-132 1155.32,-132 1155.32,-292.88 1155.32,-292.88"/>
<polygon fill="black" stroke="black" points="1151.82,-292.88 1155.32,-302.88 1158.82,-292.88 1151.82,-292.88"/>
</g>
<!-- LoggerModule&#45;&gt;UserExportModule -->
<g id="edge190" class="edge">
<title>LoggerModule&#45;&gt;UserExportModule</title>
<path fill="none" stroke="black" d="M8584.47,-187.2C8584.47,-193.03 8584.47,-198 8584.47,-198 8584.47,-198 1580.17,-198 1580.17,-198 1580.17,-198 1580.17,-325 1580.17,-325 1580.17,-325 1316.05,-325 1316.05,-325"/>
<polygon fill="black" stroke="black" points="1316.05,-321.5 1306.05,-325 1316.05,-328.5 1316.05,-321.5"/>
</g>
<!-- LoggerModule  -->
<g id="node56" class="node">
<title>LoggerModule </title>
<polygon fill="#fb8072" stroke="black" points="11399.98,-263 11296.02,-263 11296.02,-227 11399.98,-227 11399.98,-263"/>
<text text-anchor="middle" x="11348" y="-240.8" font-family="Times,serif" font-size="14.00">LoggerModule </text>
</g>
<!-- LoggerModule&#45;&gt;LoggerModule  -->
<g id="edge83" class="edge">
<title>LoggerModule&#45;&gt;LoggerModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8673.21,-154C9044.4,-154 11348,-154 11348,-154 11348,-154 11348,-216.58 11348,-216.58"/>
<polygon fill="black" stroke="black" points="11344.5,-216.58 11348,-226.58 11351.5,-216.58 11344.5,-216.58"/>
</g>
<!-- MsxLoggerService  -->
<g id="node57" class="node">
<title>MsxLoggerService </title>
<polygon fill="#fb8072" stroke="black" points="11278.29,-263 11151.71,-263 11151.71,-227 11278.29,-227 11278.29,-263"/>
<text text-anchor="middle" x="11215" y="-240.8" font-family="Times,serif" font-size="14.00">MsxLoggerService </text>
</g>
<!-- LoggerModule&#45;&gt;MsxLoggerService  -->
<g id="edge84" class="edge">
<title>LoggerModule&#45;&gt;MsxLoggerService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8673.04,-157C9033.13,-157 11215,-157 11215,-157 11215,-157 11215,-216.77 11215,-216.77"/>
<polygon fill="black" stroke="black" points="11211.5,-216.77 11215,-226.77 11218.5,-216.77 11211.5,-216.77"/>
</g>
<!-- PatchDataModule -->
<g id="node91" class="node">
<title>PatchDataModule</title>
<polygon fill="#8dd3c7" stroke="black" points="13328.58,-415 13325.58,-419 13304.58,-419 13301.58,-415 13211.42,-415 13211.42,-379 13328.58,-379 13328.58,-415"/>
<text text-anchor="middle" x="13270" y="-392.8" font-family="Times,serif" font-size="14.00">PatchDataModule</text>
</g>
<!-- LoggerModule&#45;&gt;PatchDataModule -->
<g id="edge133" class="edge">
<title>LoggerModule&#45;&gt;PatchDataModule</title>
<path fill="none" stroke="black" d="M8662.59,-187.15C8662.59,-188.3 8662.59,-189 8662.59,-189 8662.59,-189 13217.36,-189 13217.36,-189 13217.36,-189 13217.36,-368.91 13217.36,-368.91"/>
<polygon fill="black" stroke="black" points="13213.86,-368.91 13217.36,-378.91 13220.86,-368.91 13213.86,-368.91"/>
</g>
<!-- MgsSenderModule -->
<g id="node7" class="node">
<title>MgsSenderModule</title>
<polygon fill="#8dd3c7" stroke="black" points="8465.81,-122 8462.81,-126 8441.81,-126 8438.81,-122 8342.19,-122 8342.19,-86 8465.81,-86 8465.81,-122"/>
<text text-anchor="middle" x="8404" y="-99.8" font-family="Times,serif" font-size="14.00">MgsSenderModule</text>
</g>
<!-- MgsSenderModule&#45;&gt;AuthDomainModule -->
<g id="edge20" class="edge">
<title>MgsSenderModule&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M8342.28,-121C8330.63,-121 8322.01,-121 8322.01,-121 8322.01,-121 8322.01,-383 8322.01,-383 8322.01,-383 8331.53,-383 8331.53,-383"/>
<polygon fill="black" stroke="black" points="8331.53,-386.5 8341.53,-383 8331.53,-379.5 8331.53,-386.5"/>
</g>
<!-- MgsSenderModule&#45;&gt;FeatureDomainModule -->
<g id="edge57" class="edge">
<title>MgsSenderModule&#45;&gt;FeatureDomainModule</title>
<path fill="none" stroke="black" d="M8358.79,-122.06C8358.79,-169.86 8358.79,-296 8358.79,-296 8358.79,-296 8728.56,-296 8728.56,-296 8728.56,-296 8728.56,-296.68 8728.56,-296.68"/>
<polygon fill="black" stroke="black" points="8725.06,-292.83 8728.56,-302.83 8732.06,-292.83 8725.06,-292.83"/>
</g>
<!-- MgsSenderModule&#45;&gt;ListenerModule -->
<g id="edge75" class="edge">
<title>MgsSenderModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M8383.45,-122.12C8383.45,-169.43 8383.45,-293 8383.45,-293 8383.45,-293 8914.17,-293 8914.17,-293 8914.17,-293 8914.17,-512.76 8914.17,-512.76"/>
<polygon fill="black" stroke="black" points="8910.67,-512.76 8914.17,-522.76 8917.67,-512.76 8910.67,-512.76"/>
</g>
<!-- MgsSenderModule&#45;&gt;LoggerModule -->
<g id="edge82" class="edge">
<title>MgsSenderModule&#45;&gt;LoggerModule</title>
<path fill="none" stroke="black" d="M8457.43,-122.03C8457.43,-138.4 8457.43,-160 8457.43,-160 8457.43,-160 8562.88,-160 8562.88,-160"/>
<polygon fill="black" stroke="black" points="8562.88,-163.5 8572.88,-160 8562.88,-156.5 8562.88,-163.5"/>
</g>
<!-- MgsSenderModule&#45;&gt;MsxDomainModule -->
<g id="edge120" class="edge">
<title>MgsSenderModule&#45;&gt;MsxDomainModule</title>
<path fill="none" stroke="black" d="M8440.99,-122.06C8440.99,-149.87 8440.99,-200 8440.99,-200 8440.99,-200 9462.76,-200 9462.76,-200 9462.76,-200 9462.76,-216.96 9462.76,-216.96"/>
<polygon fill="black" stroke="black" points="9459.26,-216.96 9462.76,-226.96 9466.26,-216.96 9459.26,-216.96"/>
</g>
<!-- MgsSenderModule&#45;&gt;PermissionDomainModule -->
<g id="edge141" class="edge">
<title>MgsSenderModule&#45;&gt;PermissionDomainModule</title>
<path fill="none" stroke="black" d="M8350.57,-122.13C8350.57,-122.13 8350.57,-292.77 8350.57,-292.77"/>
<polygon fill="black" stroke="black" points="8347.07,-292.77 8350.57,-302.77 8354.07,-292.77 8347.07,-292.77"/>
</g>
<!-- MgsSenderModule&#45;&gt;PermissionQuerySideModule -->
<g id="edge147" class="edge">
<title>MgsSenderModule&#45;&gt;PermissionQuerySideModule</title>
<path fill="none" stroke="black" d="M8432.77,-122.41C8432.77,-152.04 8432.77,-207 8432.77,-207 8432.77,-207 9291.27,-207 9291.27,-207 9291.27,-207 9291.27,-216.89 9291.27,-216.89"/>
<polygon fill="black" stroke="black" points="9287.77,-216.89 9291.27,-226.89 9294.77,-216.89 9287.77,-216.89"/>
</g>
<!-- MgsSenderModule&#45;&gt;RoleDomainModule -->
<g id="edge158" class="edge">
<title>MgsSenderModule&#45;&gt;RoleDomainModule</title>
<path fill="none" stroke="black" d="M8367.01,-122.49C8367.01,-134.14 8367.01,-147 8367.01,-147 8367.01,-147 8565.25,-147 8565.25,-147 8565.25,-147 8565.25,-292.71 8565.25,-292.71"/>
<polygon fill="black" stroke="black" points="8561.75,-292.71 8565.25,-302.71 8568.75,-292.71 8561.75,-292.71"/>
</g>
<!-- MgsSenderModule&#45;&gt;RoleQuerySideModule -->
<g id="edge165" class="edge">
<title>MgsSenderModule&#45;&gt;RoleQuerySideModule</title>
<path fill="none" stroke="black" d="M8424.55,-122.49C8424.55,-153.59 8424.55,-213 8424.55,-213 8424.55,-213 8873.59,-213 8873.59,-213 8873.59,-213 8873.59,-216.76 8873.59,-216.76"/>
<polygon fill="black" stroke="black" points="8870.09,-216.76 8873.59,-226.76 8877.09,-216.76 8870.09,-216.76"/>
</g>
<!-- MgsSenderModule&#45;&gt;UserDomainModule -->
<g id="edge177" class="edge">
<title>MgsSenderModule&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M8391.67,-122.27C8391.67,-169.32 8391.67,-291 8391.67,-291 8391.67,-291 8957.4,-291 8957.4,-291 8957.4,-291 8957.4,-436.71 8957.4,-436.71"/>
<polygon fill="black" stroke="black" points="8953.9,-436.71 8957.4,-446.71 8960.9,-436.71 8953.9,-436.71"/>
</g>
<!-- MgsSenderModule&#45;&gt;UserQuerySideModule -->
<g id="edge200" class="edge">
<title>MgsSenderModule&#45;&gt;UserQuerySideModule</title>
<path fill="none" stroke="black" d="M8399.89,-122.17C8399.89,-168.98 8399.89,-290 8399.89,-290 8399.89,-290 9106.53,-290 9106.53,-290 9106.53,-290 9106.53,-292.97 9106.53,-292.97"/>
<polygon fill="black" stroke="black" points="9103.03,-292.97 9106.53,-302.97 9110.03,-292.97 9103.03,-292.97"/>
</g>
<!-- MgsSenderModule&#45;&gt;ApplicationModule -->
<g id="edge8" class="edge">
<title>MgsSenderModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M8408.11,-122.22C8408.11,-168.5 8408.11,-287 8408.11,-287 8408.11,-287 9573.29,-287 9573.29,-287 9573.29,-287 9573.29,-512.73 9573.29,-512.73"/>
<polygon fill="black" stroke="black" points="9569.79,-512.73 9573.29,-522.73 9576.79,-512.73 9569.79,-512.73"/>
</g>
<!-- MgsSenderModule&#45;&gt;AuthQuerySideModule -->
<g id="edge39" class="edge">
<title>MgsSenderModule&#45;&gt;AuthQuerySideModule</title>
<path fill="none" stroke="black" d="M8449.21,-122.01C8449.21,-148.02 8449.21,-193 8449.21,-193 8449.21,-193 12883.58,-193 12883.58,-193 12883.58,-193 12883.58,-216.81 12883.58,-216.81"/>
<polygon fill="black" stroke="black" points="12880.08,-216.81 12883.58,-226.81 12887.08,-216.81 12880.08,-216.81"/>
</g>
<!-- MgsSenderModule&#45;&gt;MsxQuerySideModule -->
<g id="edge126" class="edge">
<title>MgsSenderModule&#45;&gt;MsxQuerySideModule</title>
<path fill="none" stroke="black" d="M8375.23,-122.21C8375.23,-159.06 8375.23,-239 8375.23,-239 8375.23,-239 8312.19,-239 8312.19,-239"/>
<polygon fill="black" stroke="black" points="8312.19,-235.5 8302.19,-239 8312.19,-242.5 8312.19,-235.5"/>
</g>
<!-- MgsSenderModule&#45;&gt;MasterQuerySideModule -->
<g id="edge88" class="edge">
<title>MgsSenderModule&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M8342.11,-88C7637.25,-88 1149.44,-88 1149.44,-88 1149.44,-88 1149.44,-292.72 1149.44,-292.72"/>
<polygon fill="black" stroke="black" points="1145.94,-292.72 1149.44,-302.72 1152.94,-292.72 1145.94,-292.72"/>
</g>
<!-- MgsSenderModule&#45;&gt;UserExportModule -->
<g id="edge191" class="edge">
<title>MgsSenderModule&#45;&gt;UserExportModule</title>
<path fill="none" stroke="black" d="M8342.3,-89C7656.34,-89 1481.14,-89 1481.14,-89 1481.14,-89 1481.14,-318 1481.14,-318 1481.14,-318 1316.31,-318 1316.31,-318"/>
<polygon fill="black" stroke="black" points="1316.31,-314.5 1306.31,-318 1316.31,-321.5 1316.31,-314.5"/>
</g>
<!-- CareClient  -->
<g id="node64" class="node">
<title>CareClient </title>
<polygon fill="#fb8072" stroke="black" points="7801.15,-187 7720.85,-187 7720.85,-151 7801.15,-151 7801.15,-187"/>
<text text-anchor="middle" x="7761" y="-164.8" font-family="Times,serif" font-size="14.00">CareClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;CareClient  -->
<g id="edge96" class="edge">
<title>MgsSenderModule&#45;&gt;CareClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.05,-118C8180.9,-118 7761,-118 7761,-118 7761,-118 7761,-140.88 7761,-140.88"/>
<polygon fill="black" stroke="black" points="7757.5,-140.88 7761,-150.88 7764.5,-140.88 7757.5,-140.88"/>
</g>
<!-- CustomerClient  -->
<g id="node65" class="node">
<title>CustomerClient </title>
<polygon fill="#fb8072" stroke="black" points="7703.16,-187 7594.84,-187 7594.84,-151 7703.16,-151 7703.16,-187"/>
<text text-anchor="middle" x="7649" y="-164.8" font-family="Times,serif" font-size="14.00">CustomerClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;CustomerClient  -->
<g id="edge97" class="edge">
<title>MgsSenderModule&#45;&gt;CustomerClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.22,-117C8161.9,-117 7649,-117 7649,-117 7649,-117 7649,-140.81 7649,-140.81"/>
<polygon fill="black" stroke="black" points="7645.5,-140.81 7649,-150.81 7652.5,-140.81 7645.5,-140.81"/>
</g>
<!-- EmployeeClient  -->
<g id="node66" class="node">
<title>EmployeeClient </title>
<polygon fill="#fb8072" stroke="black" points="7577.49,-187 7466.51,-187 7466.51,-151 7577.49,-151 7577.49,-187"/>
<text text-anchor="middle" x="7522" y="-164.8" font-family="Times,serif" font-size="14.00">EmployeeClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;EmployeeClient  -->
<g id="edge98" class="edge">
<title>MgsSenderModule&#45;&gt;EmployeeClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.33,-116C8141.94,-116 7522,-116 7522,-116 7522,-116 7522,-140.76 7522,-140.76"/>
<polygon fill="black" stroke="black" points="7518.5,-140.76 7522,-150.76 7525.5,-140.76 7518.5,-140.76"/>
</g>
<!-- LoggerClient  -->
<g id="node67" class="node">
<title>LoggerClient </title>
<polygon fill="#fb8072" stroke="black" points="7449.15,-187 7354.85,-187 7354.85,-151 7449.15,-151 7449.15,-187"/>
<text text-anchor="middle" x="7402" y="-164.8" font-family="Times,serif" font-size="14.00">LoggerClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;LoggerClient  -->
<g id="edge99" class="edge">
<title>MgsSenderModule&#45;&gt;LoggerClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.08,-114C8123.62,-114 7402,-114 7402,-114 7402,-114 7402,-140.99 7402,-140.99"/>
<polygon fill="black" stroke="black" points="7398.5,-140.99 7402,-150.99 7405.5,-140.99 7398.5,-140.99"/>
</g>
<!-- MailerClient  -->
<g id="node68" class="node">
<title>MailerClient </title>
<polygon fill="#fb8072" stroke="black" points="7336.54,-187 7245.46,-187 7245.46,-151 7336.54,-151 7336.54,-187"/>
<text text-anchor="middle" x="7291" y="-164.8" font-family="Times,serif" font-size="14.00">MailerClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;MailerClient  -->
<g id="edge100" class="edge">
<title>MgsSenderModule&#45;&gt;MailerClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.18,-113C8108.32,-113 7291,-113 7291,-113 7291,-113 7291,-140.97 7291,-140.97"/>
<polygon fill="black" stroke="black" points="7287.5,-140.97 7291,-150.97 7294.5,-140.97 7287.5,-140.97"/>
</g>
<!-- MsgSenderService  -->
<g id="node69" class="node">
<title>MsgSenderService </title>
<polygon fill="#fb8072" stroke="black" points="7227.74,-187 7102.26,-187 7102.26,-151 7227.74,-151 7227.74,-187"/>
<text text-anchor="middle" x="7165" y="-164.8" font-family="Times,serif" font-size="14.00">MsgSenderService </text>
</g>
<!-- MgsSenderModule&#45;&gt;MsgSenderService  -->
<g id="edge101" class="edge">
<title>MgsSenderModule&#45;&gt;MsgSenderService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.13,-111C8091.51,-111 7165,-111 7165,-111 7165,-111 7165,-140.97 7165,-140.97"/>
<polygon fill="black" stroke="black" points="7161.5,-140.97 7165,-150.97 7168.5,-140.97 7161.5,-140.97"/>
</g>
<!-- NotificationClient  -->
<g id="node70" class="node">
<title>NotificationClient </title>
<polygon fill="#fb8072" stroke="black" points="7084.87,-187 6963.13,-187 6963.13,-151 7084.87,-151 7084.87,-187"/>
<text text-anchor="middle" x="7024" y="-164.8" font-family="Times,serif" font-size="14.00">NotificationClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;NotificationClient  -->
<g id="edge102" class="edge">
<title>MgsSenderModule&#45;&gt;NotificationClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.02,-110C8073.5,-110 7024,-110 7024,-110 7024,-110 7024,-140.98 7024,-140.98"/>
<polygon fill="black" stroke="black" points="7020.5,-140.98 7024,-150.98 7027.5,-140.98 7020.5,-140.98"/>
</g>
<!-- NotifierClient  -->
<g id="node71" class="node">
<title>NotifierClient </title>
<polygon fill="#fb8072" stroke="black" points="6945.04,-187 6846.96,-187 6846.96,-151 6945.04,-151 6945.04,-187"/>
<text text-anchor="middle" x="6896" y="-164.8" font-family="Times,serif" font-size="14.00">NotifierClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;NotifierClient  -->
<g id="edge103" class="edge">
<title>MgsSenderModule&#45;&gt;NotifierClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.07,-109C8058.31,-109 6896,-109 6896,-109 6896,-109 6896,-141 6896,-141"/>
<polygon fill="black" stroke="black" points="6892.5,-141 6896,-151 6899.5,-141 6892.5,-141"/>
</g>
<!-- OrgchartClient  -->
<g id="node72" class="node">
<title>OrgchartClient </title>
<polygon fill="#fb8072" stroke="black" points="6828.97,-187 6725.03,-187 6725.03,-151 6828.97,-151 6828.97,-187"/>
<text text-anchor="middle" x="6777" y="-164.8" font-family="Times,serif" font-size="14.00">OrgchartClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;OrgchartClient  -->
<g id="edge104" class="edge">
<title>MgsSenderModule&#45;&gt;OrgchartClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.21,-107C8045.02,-107 6777,-107 6777,-107 6777,-107 6777,-140.73 6777,-140.73"/>
<polygon fill="black" stroke="black" points="6773.5,-140.73 6777,-150.73 6780.5,-140.73 6773.5,-140.73"/>
</g>
<!-- PropertyClient  -->
<g id="node73" class="node">
<title>PropertyClient </title>
<polygon fill="#fb8072" stroke="black" points="6706.93,-187 6605.07,-187 6605.07,-151 6706.93,-151 6706.93,-187"/>
<text text-anchor="middle" x="6656" y="-164.8" font-family="Times,serif" font-size="14.00">PropertyClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;PropertyClient  -->
<g id="edge105" class="edge">
<title>MgsSenderModule&#45;&gt;PropertyClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.3,-106C8031.91,-106 6656,-106 6656,-106 6656,-106 6656,-140.78 6656,-140.78"/>
<polygon fill="black" stroke="black" points="6652.5,-140.78 6656,-150.78 6659.5,-140.78 6652.5,-140.78"/>
</g>
<!-- StsClient  -->
<g id="node74" class="node">
<title>StsClient </title>
<polygon fill="#fb8072" stroke="black" points="7889.34,-187 7818.66,-187 7818.66,-151 7889.34,-151 7889.34,-187"/>
<text text-anchor="middle" x="7854" y="-164.8" font-family="Times,serif" font-size="14.00">StsClient </text>
</g>
<!-- MgsSenderModule&#45;&gt;StsClient  -->
<g id="edge106" class="edge">
<title>MgsSenderModule&#45;&gt;StsClient </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8342.24,-120C8198.56,-120 7854,-120 7854,-120 7854,-120 7854,-140.75 7854,-140.75"/>
<polygon fill="black" stroke="black" points="7850.5,-140.75 7854,-150.75 7857.5,-140.75 7850.5,-140.75"/>
</g>
<!-- MgsSenderModule&#45;&gt;PatchDataModule -->
<g id="edge134" class="edge">
<title>MgsSenderModule&#45;&gt;PatchDataModule</title>
<path fill="none" stroke="black" d="M8416.33,-122.16C8416.33,-164.26 8416.33,-265 8416.33,-265 8416.33,-265 13215.39,-265 13215.39,-265 13215.39,-265 13215.39,-368.92 13215.39,-368.92"/>
<polygon fill="black" stroke="black" points="13211.89,-368.92 13215.39,-378.92 13218.89,-368.92 13211.89,-368.92"/>
</g>
<!-- MsxDomainModule&#45;&gt;ApplicationModule -->
<g id="edge9" class="edge">
<title>MsxDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M9499.74,-245C9548.71,-245 9607.56,-245 9607.56,-245 9607.56,-245 9607.56,-512.93 9607.56,-512.93"/>
<polygon fill="black" stroke="black" points="9604.06,-512.93 9607.56,-522.93 9611.06,-512.93 9604.06,-512.93"/>
</g>
<!-- PermissionDomainModule&#45;&gt;ApplicationModule -->
<g id="edge10" class="edge">
<title>PermissionDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M8491.38,-339.15C8491.38,-351.27 8491.38,-365 8491.38,-365 8491.38,-365 9539.02,-365 9539.02,-365 9539.02,-365 9539.02,-512.83 9539.02,-512.83"/>
<polygon fill="black" stroke="black" points="9535.52,-512.83 9539.02,-522.83 9542.52,-512.83 9535.52,-512.83"/>
</g>
<!-- PermissionQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge11" class="edge">
<title>PermissionQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M9330.64,-263.07C9330.64,-325.31 9330.64,-527 9330.64,-527 9330.64,-527 9506.33,-527 9506.33,-527"/>
<polygon fill="black" stroke="black" points="9506.33,-530.5 9516.33,-527 9506.33,-523.5 9506.33,-530.5"/>
</g>
<!-- PermissionQueryRepository  -->
<g id="node96" class="node">
<title>PermissionQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="11416.14,-339 11237.86,-339 11237.86,-303 11416.14,-303 11416.14,-339"/>
<text text-anchor="middle" x="11327" y="-316.8" font-family="Times,serif" font-size="14.00">PermissionQueryRepository </text>
</g>
<!-- PermissionQuerySideModule&#45;&gt;PermissionQueryRepository  -->
<g id="edge149" class="edge">
<title>PermissionQuerySideModule&#45;&gt;PermissionQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9267.1,-263.06C9267.1,-267.49 9267.1,-271 9267.1,-271 9267.1,-271 11258.16,-271 11258.16,-271 11258.16,-271 11258.16,-292.95 11258.16,-292.95"/>
<polygon fill="black" stroke="black" points="11254.66,-292.95 11258.16,-302.95 11261.66,-292.95 11254.66,-292.95"/>
</g>
<!-- PermissionQuerySideModule  -->
<g id="node97" class="node">
<title>PermissionQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="11619.64,-339 11434.36,-339 11434.36,-303 11619.64,-303 11619.64,-339"/>
<text text-anchor="middle" x="11527" y="-316.8" font-family="Times,serif" font-size="14.00">PermissionQuerySideModule </text>
</g>
<!-- PermissionQuerySideModule&#45;&gt;PermissionQuerySideModule  -->
<g id="edge150" class="edge">
<title>PermissionQuerySideModule&#45;&gt;PermissionQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9288.28,-263.17C9288.28,-267.05 9288.28,-270 9288.28,-270 9288.28,-270 11527,-270 11527,-270 11527,-270 11527,-292.88 11527,-292.88"/>
<polygon fill="black" stroke="black" points="11523.5,-292.88 11527,-302.88 11530.5,-292.88 11523.5,-292.88"/>
</g>
<!-- RedisCacheModule -->
<g id="node11" class="node">
<title>RedisCacheModule</title>
<polygon fill="#8dd3c7" stroke="black" points="8768.42,-263 8765.42,-267 8744.42,-267 8741.42,-263 8641.58,-263 8641.58,-227 8768.42,-227 8768.42,-263"/>
<text text-anchor="middle" x="8705" y="-240.8" font-family="Times,serif" font-size="14.00">RedisCacheModule</text>
</g>
<!-- RedisCacheModule&#45;&gt;ListenerModule -->
<g id="edge76" class="edge">
<title>RedisCacheModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M8663.59,-263.39C8663.59,-326.73 8663.59,-532 8663.59,-532 8663.59,-532 8895.03,-532 8895.03,-532"/>
<polygon fill="black" stroke="black" points="8895.03,-535.5 8905.03,-532 8895.03,-528.5 8895.03,-535.5"/>
</g>
<!-- RedisCacheModule&#45;&gt;RoleDomainModule -->
<g id="edge159" class="edge">
<title>RedisCacheModule&#45;&gt;RoleDomainModule</title>
<path fill="none" stroke="black" d="M8647.04,-263.01C8647.04,-263.01 8647.04,-292.85 8647.04,-292.85"/>
<polygon fill="black" stroke="black" points="8643.54,-292.85 8647.04,-302.85 8650.54,-292.85 8643.54,-292.85"/>
</g>
<!-- RedisCacheModule&#45;&gt;UserDomainModule -->
<g id="edge178" class="edge">
<title>RedisCacheModule&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M8669.64,-263.16C8669.64,-314.67 8669.64,-458 8669.64,-458 8669.64,-458 8925.14,-458 8925.14,-458"/>
<polygon fill="black" stroke="black" points="8925.14,-461.5 8935.14,-458 8925.14,-454.5 8925.14,-461.5"/>
</g>
<!-- RedisCacheModule&#45;&gt;UserQuerySideModule -->
<g id="edge202" class="edge">
<title>RedisCacheModule&#45;&gt;UserQuerySideModule</title>
<path fill="none" stroke="black" d="M8741.77,-263.15C8741.77,-275.27 8741.77,-289 8741.77,-289 8741.77,-289 9112.98,-289 9112.98,-289 9112.98,-289 9112.98,-292.76 9112.98,-292.76"/>
<polygon fill="black" stroke="black" points="9109.48,-292.76 9112.98,-302.76 9116.48,-292.76 9109.48,-292.76"/>
</g>
<!-- RedisCacheModule&#45;&gt;ApplicationModule -->
<g id="edge12" class="edge">
<title>RedisCacheModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M8754.99,-263.1C8754.99,-274.09 8754.99,-286 8754.99,-286 8754.99,-286 9584.71,-286 9584.71,-286 9584.71,-286 9584.71,-512.66 9584.71,-512.66"/>
<polygon fill="black" stroke="black" points="9581.21,-512.66 9584.71,-522.66 9588.21,-512.66 9581.21,-512.66"/>
</g>
<!-- RedisCacheModule&#45;&gt;MasterQuerySideModule -->
<g id="edge90" class="edge">
<title>RedisCacheModule&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M8696.79,-226.79C8696.79,-221.45 8696.79,-217 8696.79,-217 8696.79,-217 1158.26,-217 1158.26,-217 1158.26,-217 1158.26,-292.88 1158.26,-292.88"/>
<polygon fill="black" stroke="black" points="1154.76,-292.88 1158.26,-302.88 1161.76,-292.88 1154.76,-292.88"/>
</g>
<!-- RedisCacheService  -->
<g id="node99" class="node">
<title>RedisCacheService </title>
<polygon fill="#fb8072" stroke="black" points="11984.34,-339 11855.66,-339 11855.66,-303 11984.34,-303 11984.34,-339"/>
<text text-anchor="middle" x="11920" y="-316.8" font-family="Times,serif" font-size="14.00">RedisCacheService </text>
</g>
<!-- RedisCacheModule&#45;&gt;RedisCacheService  -->
<g id="edge154" class="edge">
<title>RedisCacheModule&#45;&gt;RedisCacheService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8720.6,-226.88C8720.6,-212.22 8720.6,-194 8720.6,-194 8720.6,-194 11920,-194 11920,-194 11920,-194 11920,-292.8 11920,-292.8"/>
<polygon fill="black" stroke="black" points="11916.5,-292.8 11920,-302.8 11923.5,-292.8 11916.5,-292.8"/>
</g>
<!-- RoleDomainModule&#45;&gt;ApplicationModule -->
<g id="edge13" class="edge">
<title>RoleDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M8592,-339.06C8592,-350.44 8592,-363 8592,-363 8592,-363 9550.44,-363 9550.44,-363 9550.44,-363 9550.44,-512.95 9550.44,-512.95"/>
<polygon fill="black" stroke="black" points="9546.94,-512.95 9550.44,-522.95 9553.94,-512.95 9546.94,-512.95"/>
</g>
<!-- RoleQuerySideModule&#45;&gt;ListenerModule -->
<g id="edge77" class="edge">
<title>RoleQuerySideModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M8923.05,-263.07C8923.05,-263.07 8923.05,-512.79 8923.05,-512.79"/>
<polygon fill="black" stroke="black" points="8919.55,-512.79 8923.05,-522.79 8926.55,-512.79 8919.55,-512.79"/>
</g>
<!-- RoleQuerySideModule&#45;&gt;PermissionDomainModule -->
<g id="edge142" class="edge">
<title>RoleQuerySideModule&#45;&gt;PermissionDomainModule</title>
<path fill="none" stroke="black" d="M8798.15,-263.25C8798.15,-277.18 8798.15,-294 8798.15,-294 8798.15,-294 8501.39,-294 8501.39,-294 8501.39,-294 8501.39,-294.88 8501.39,-294.88"/>
<polygon fill="black" stroke="black" points="8497.89,-292.84 8501.39,-302.84 8504.89,-292.84 8497.89,-292.84"/>
</g>
<!-- RoleQuerySideModule&#45;&gt;RoleDomainModule -->
<g id="edge160" class="edge">
<title>RoleQuerySideModule&#45;&gt;RoleDomainModule</title>
<path fill="none" stroke="black" d="M8810.23,-263.04C8810.23,-279.73 8810.23,-302 8810.23,-302 8810.23,-302 8652.3,-302 8652.3,-302 8652.3,-302 8652.3,-302.08 8652.3,-302.08"/>
<polygon fill="black" stroke="black" points="8648.8,-292.82 8652.3,-302.82 8655.8,-292.82 8648.8,-292.82"/>
</g>
<!-- RoleQuerySideModule&#45;&gt;UserDomainModule -->
<g id="edge179" class="edge">
<title>RoleQuerySideModule&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M8926.01,-263.09C8926.01,-313.7 8926.01,-453 8926.01,-453 8926.01,-453 8926.93,-453 8926.93,-453"/>
<polygon fill="black" stroke="black" points="8925.21,-456.5 8935.21,-453 8925.21,-449.5 8925.21,-456.5"/>
</g>
<!-- RoleQuerySideModule&#45;&gt;UserQuerySideModule -->
<g id="edge203" class="edge">
<title>RoleQuerySideModule&#45;&gt;UserQuerySideModule</title>
<path fill="none" stroke="black" d="M8928.97,-263.04C8928.97,-284.66 8928.97,-318 8928.97,-318 8928.97,-318 9090.11,-318 9090.11,-318"/>
<polygon fill="black" stroke="black" points="9090.11,-321.5 9100.11,-318 9090.11,-314.5 9090.11,-321.5"/>
</g>
<!-- RoleQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge14" class="edge">
<title>RoleQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M8920.09,-263.13C8920.09,-273.71 8920.09,-285 8920.09,-285 8920.09,-285 9596.13,-285 9596.13,-285 9596.13,-285 9596.13,-513 9596.13,-513"/>
<polygon fill="black" stroke="black" points="9592.63,-513 9596.13,-523 9599.63,-513 9592.63,-513"/>
</g>
<!-- RoleQuerySideModule&#45;&gt;UserExportModule -->
<g id="edge193" class="edge">
<title>RoleQuerySideModule&#45;&gt;UserExportModule</title>
<path fill="none" stroke="black" d="M8836.14,-263.12C8836.14,-292.91 8836.14,-349 8836.14,-349 8836.14,-349 1266.45,-349 1266.45,-349 1266.45,-349 1266.45,-348.02 1266.45,-348.02"/>
<polygon fill="black" stroke="black" points="1269.95,-349.21 1266.45,-339.21 1262.95,-349.21 1269.95,-349.21"/>
</g>
<!-- RoleQueryRepository  -->
<g id="node103" class="node">
<title>RoleQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="12656.36,-339 12513.64,-339 12513.64,-303 12656.36,-303 12656.36,-339"/>
<text text-anchor="middle" x="12585" y="-316.8" font-family="Times,serif" font-size="14.00">RoleQueryRepository </text>
</g>
<!-- RoleQuerySideModule&#45;&gt;RoleQueryRepository  -->
<g id="edge167" class="edge">
<title>RoleQuerySideModule&#45;&gt;RoleQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8877.63,-263.08C8877.63,-264.85 8877.63,-266 8877.63,-266 8877.63,-266 12585,-266 12585,-266 12585,-266 12585,-292.99 12585,-292.99"/>
<polygon fill="black" stroke="black" points="12581.5,-292.99 12585,-302.99 12588.5,-292.99 12581.5,-292.99"/>
</g>
<!-- RoleQuerySideModule  -->
<g id="node104" class="node">
<title>RoleQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="12495.86,-339 12346.14,-339 12346.14,-303 12495.86,-303 12495.86,-339"/>
<text text-anchor="middle" x="12421" y="-316.8" font-family="Times,serif" font-size="14.00">RoleQuerySideModule </text>
</g>
<!-- RoleQuerySideModule&#45;&gt;RoleQuerySideModule  -->
<g id="edge168" class="edge">
<title>RoleQuerySideModule&#45;&gt;RoleQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8863.8,-263.17C8863.8,-265.45 8863.8,-267 8863.8,-267 8863.8,-267 12421,-267 12421,-267 12421,-267 12421,-292.72 12421,-292.72"/>
<polygon fill="black" stroke="black" points="12417.5,-292.72 12421,-302.72 12424.5,-292.72 12417.5,-292.72"/>
</g>
<!-- RoleService  -->
<g id="node105" class="node">
<title>RoleService </title>
<polygon fill="#fb8072" stroke="black" points="12327.92,-339 12240.08,-339 12240.08,-303 12327.92,-303 12327.92,-339"/>
<text text-anchor="middle" x="12284" y="-316.8" font-family="Times,serif" font-size="14.00">RoleService </text>
</g>
<!-- RoleQuerySideModule&#45;&gt;RoleService  -->
<g id="edge169" class="edge">
<title>RoleQuerySideModule&#45;&gt;RoleService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8849.97,-263.12C8849.97,-265.97 8849.97,-268 8849.97,-268 8849.97,-268 12284,-268 12284,-268 12284,-268 12284,-292.76 12284,-292.76"/>
<polygon fill="black" stroke="black" points="12280.5,-292.76 12284,-302.76 12287.5,-292.76 12280.5,-292.76"/>
</g>
<!-- UserDomainModule&#45;&gt;ListenerModule -->
<g id="edge78" class="edge">
<title>UserDomainModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M8973.08,-483.01C8973.08,-483.01 8973.08,-512.85 8973.08,-512.85"/>
<polygon fill="black" stroke="black" points="8969.58,-512.85 8973.08,-522.85 8976.58,-512.85 8969.58,-512.85"/>
</g>
<!-- UserDomainModule&#45;&gt;ApplicationModule -->
<g id="edge15" class="edge">
<title>UserDomainModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M9038.62,-483.24C9038.62,-508.48 9038.62,-551 9038.62,-551 9038.62,-551 9506.22,-551 9506.22,-551"/>
<polygon fill="black" stroke="black" points="9506.22,-554.5 9516.22,-551 9506.22,-547.5 9506.22,-554.5"/>
</g>
<!-- UserDomainModule  -->
<g id="node107" class="node">
<title>UserDomainModule </title>
<polygon fill="#fb8072" stroke="black" points="10424.08,-559 10289.92,-559 10289.92,-523 10424.08,-523 10424.08,-559"/>
<text text-anchor="middle" x="10357" y="-536.8" font-family="Times,serif" font-size="14.00">UserDomainModule </text>
</g>
<!-- UserDomainModule&#45;&gt;UserDomainModule  -->
<g id="edge181" class="edge">
<title>UserDomainModule&#45;&gt;UserDomainModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9066.89,-455C9330.33,-455 10293.77,-455 10293.77,-455 10293.77,-455 10293.77,-512.76 10293.77,-512.76"/>
<polygon fill="black" stroke="black" points="10290.27,-512.76 10293.77,-522.76 10297.27,-512.76 10290.27,-512.76"/>
</g>
<!-- UserDomainService  -->
<g id="node108" class="node">
<title>UserDomainService </title>
<polygon fill="#fb8072" stroke="black" points="10271.51,-559 10138.49,-559 10138.49,-523 10271.51,-523 10271.51,-559"/>
<text text-anchor="middle" x="10205" y="-536.8" font-family="Times,serif" font-size="14.00">UserDomainService </text>
</g>
<!-- UserDomainModule&#45;&gt;UserDomainService  -->
<g id="edge182" class="edge">
<title>UserDomainModule&#45;&gt;UserDomainService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9066.59,-462C9317.84,-462 10205,-462 10205,-462 10205,-462 10205,-512.68 10205,-512.68"/>
<polygon fill="black" stroke="black" points="10201.5,-512.68 10205,-522.68 10208.5,-512.68 10201.5,-512.68"/>
</g>
<!-- UserQuerySideModule&#45;&gt;AuthDomainModule -->
<g id="edge21" class="edge">
<title>UserQuerySideModule&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M9191.23,-339.01C9191.23,-361.49 9191.23,-397 9191.23,-397 9191.23,-397 8484.44,-397 8484.44,-397"/>
<polygon fill="black" stroke="black" points="8484.44,-393.5 8474.44,-397 8484.44,-400.5 8484.44,-393.5"/>
</g>
<!-- UserQuerySideModule&#45;&gt;ListenerModule -->
<g id="edge80" class="edge">
<title>UserQuerySideModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M9209.46,-339.11C9209.46,-392.74 9209.46,-547 9209.46,-547 9209.46,-547 9020.71,-547 9020.71,-547"/>
<polygon fill="black" stroke="black" points="9020.71,-543.5 9010.71,-547 9020.71,-550.5 9020.71,-543.5"/>
</g>
<!-- UserQuerySideModule&#45;&gt;UserDomainModule -->
<g id="edge180" class="edge">
<title>UserQuerySideModule&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M9099.91,-332C9075.24,-332 9053.83,-332 9053.83,-332 9053.83,-332 9053.83,-436.79 9053.83,-436.79"/>
<polygon fill="black" stroke="black" points="9050.33,-436.79 9053.83,-446.79 9057.33,-436.79 9050.33,-436.79"/>
</g>
<!-- UserQuerySideModule&#45;&gt;ApplicationModule -->
<g id="edge16" class="edge">
<title>UserQuerySideModule&#45;&gt;ApplicationModule</title>
<path fill="none" stroke="black" d="M9246.14,-321C9358.79,-321 9561.87,-321 9561.87,-321 9561.87,-321 9561.87,-513 9561.87,-513"/>
<polygon fill="black" stroke="black" points="9558.37,-513 9561.87,-523 9565.37,-513 9558.37,-513"/>
</g>
<!-- UserQueryRepository  -->
<g id="node115" class="node">
<title>UserQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="10440.35,-415 10297.65,-415 10297.65,-379 10440.35,-379 10440.35,-415"/>
<text text-anchor="middle" x="10369" y="-392.8" font-family="Times,serif" font-size="14.00">UserQueryRepository </text>
</g>
<!-- UserQuerySideModule&#45;&gt;UserQueryRepository  -->
<g id="edge204" class="edge">
<title>UserQuerySideModule&#45;&gt;UserQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9154.77,-339.04C9154.77,-346.75 9154.77,-354 9154.77,-354 9154.77,-354 10369,-354 10369,-354 10369,-354 10369,-368.51 10369,-368.51"/>
<polygon fill="black" stroke="black" points="10365.5,-368.51 10369,-378.51 10372.5,-368.51 10365.5,-368.51"/>
</g>
<!-- UserQuerySideModule  -->
<g id="node116" class="node">
<title>UserQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="10279.85,-415 10130.15,-415 10130.15,-379 10279.85,-379 10279.85,-415"/>
<text text-anchor="middle" x="10205" y="-392.8" font-family="Times,serif" font-size="14.00">UserQuerySideModule </text>
</g>
<!-- UserQuerySideModule&#45;&gt;UserQuerySideModule  -->
<g id="edge205" class="edge">
<title>UserQuerySideModule&#45;&gt;UserQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9136.54,-339.32C9136.54,-347.79 9136.54,-356 9136.54,-356 9136.54,-356 10162.12,-356 10162.12,-356 10162.12,-356 10162.12,-368.9 10162.12,-368.9"/>
<polygon fill="black" stroke="black" points="10158.62,-368.9 10162.12,-378.9 10165.62,-368.9 10158.62,-368.9"/>
</g>
<!-- UserService  -->
<g id="node117" class="node">
<title>UserService </title>
<polygon fill="#fb8072" stroke="black" points="10111.9,-415 10024.1,-415 10024.1,-379 10111.9,-379 10111.9,-415"/>
<text text-anchor="middle" x="10068" y="-392.8" font-family="Times,serif" font-size="14.00">UserService </text>
</g>
<!-- UserQuerySideModule&#45;&gt;UserService  -->
<g id="edge206" class="edge">
<title>UserQuerySideModule&#45;&gt;UserService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9118.31,-339.07C9118.31,-348.46 9118.31,-358 9118.31,-358 9118.31,-358 10037.25,-358 10037.25,-358 10037.25,-358 10037.25,-368.87 10037.25,-368.87"/>
<polygon fill="black" stroke="black" points="10033.75,-368.87 10037.25,-378.87 10040.75,-368.87 10033.75,-368.87"/>
</g>
<!-- DomainDatabaseModule -->
<g id="node17" class="node">
<title>DomainDatabaseModule</title>
<polygon fill="#8dd3c7" stroke="black" points="3630.95,-187 3627.95,-191 3606.95,-191 3603.95,-187 3475.05,-187 3475.05,-151 3630.95,-151 3630.95,-187"/>
<text text-anchor="middle" x="3553" y="-164.8" font-family="Times,serif" font-size="14.00">DomainDatabaseModule</text>
</g>
<!-- DomainDatabaseModule&#45;&gt;AuthDomainModule -->
<g id="edge18" class="edge">
<title>DomainDatabaseModule&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M3497.3,-187.13C3497.3,-239.28 3497.3,-386 3497.3,-386 3497.3,-386 8331.84,-386 8331.84,-386"/>
<polygon fill="black" stroke="black" points="8331.84,-389.5 8341.84,-386 8331.84,-382.5 8331.84,-389.5"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;FeatureDomainModule -->
<g id="edge54" class="edge">
<title>DomainDatabaseModule&#45;&gt;FeatureDomainModule</title>
<path fill="none" stroke="black" d="M3519.58,-187.07C3519.58,-231.78 3519.58,-344 3519.58,-344 3519.58,-344 8724.56,-344 8724.56,-344 8724.56,-344 8724.56,-343.51 8724.56,-343.51"/>
<polygon fill="black" stroke="black" points="8728.06,-349.12 8724.56,-339.12 8721.06,-349.12 8728.06,-349.12"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;MsxDomainModule -->
<g id="edge118" class="edge">
<title>DomainDatabaseModule&#45;&gt;MsxDomainModule</title>
<path fill="none" stroke="black" d="M3564.14,-187.32C3564.14,-195.79 3564.14,-204 3564.14,-204 3564.14,-204 9407.24,-204 9407.24,-204 9407.24,-204 9407.24,-216.9 9407.24,-216.9"/>
<polygon fill="black" stroke="black" points="9403.74,-216.9 9407.24,-226.9 9410.74,-216.9 9403.74,-216.9"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;PermissionDomainModule -->
<g id="edge138" class="edge">
<title>DomainDatabaseModule&#45;&gt;PermissionDomainModule</title>
<path fill="none" stroke="black" d="M3608.7,-187.2C3608.7,-228.35 3608.7,-325 3608.7,-325 3608.7,-325 8331.4,-325 8331.4,-325"/>
<polygon fill="black" stroke="black" points="8331.4,-328.5 8341.4,-325 8331.4,-321.5 8331.4,-328.5"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;RoleDomainModule -->
<g id="edge156" class="edge">
<title>DomainDatabaseModule&#45;&gt;RoleDomainModule</title>
<path fill="none" stroke="black" d="M3541.86,-187.19C3541.86,-202.18 3541.86,-221 3541.86,-221 3541.86,-221 8541.97,-221 8541.97,-221 8541.97,-221 8541.97,-292.66 8541.97,-292.66"/>
<polygon fill="black" stroke="black" points="8538.47,-292.66 8541.97,-302.66 8545.47,-292.66 8538.47,-292.66"/>
</g>
<!-- DomainDatabaseModule&#45;&gt;UserDomainModule -->
<g id="edge175" class="edge">
<title>DomainDatabaseModule&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M3586.42,-187.39C3586.42,-251.75 3586.42,-463 3586.42,-463 3586.42,-463 8925.46,-463 8925.46,-463"/>
<polygon fill="black" stroke="black" points="8925.46,-466.5 8935.46,-463 8925.46,-459.5 8925.46,-466.5"/>
</g>
<!-- AuthEventStreamRepository -->
<g id="node20" class="node">
<title>AuthEventStreamRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="3076" cy="-169" rx="122.71" ry="18"/>
<text text-anchor="middle" x="3076" y="-164.8" font-family="Times,serif" font-size="14.00">AuthEventStreamRepository</text>
</g>
<!-- AuthEventStreamRepository&#45;&gt;AuthDomainModule -->
<g id="edge24" class="edge">
<title>AuthEventStreamRepository&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M3076,-187.19C3076,-241.06 3076,-396 3076,-396 3076,-396 8331.55,-396 8331.55,-396"/>
<polygon fill="black" stroke="black" points="8331.55,-399.5 8341.55,-396 8331.55,-392.5 8331.55,-399.5"/>
</g>
<!-- AuthSaga -->
<g id="node21" class="node">
<title>AuthSaga</title>
<ellipse fill="#fdb462" stroke="black" cx="3402" cy="-169" rx="49.24" ry="18"/>
<text text-anchor="middle" x="3402" y="-164.8" font-family="Times,serif" font-size="14.00">AuthSaga</text>
</g>
<!-- AuthSaga&#45;&gt;AuthDomainModule -->
<g id="edge25" class="edge">
<title>AuthSaga&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M3418.37,-186.26C3418.37,-238.13 3418.37,-389 3418.37,-389 3418.37,-389 8331.7,-389 8331.7,-389"/>
<polygon fill="black" stroke="black" points="8331.7,-392.5 8341.7,-389 8331.7,-385.5 8331.7,-392.5"/>
</g>
<!-- AuthSaga&#45;&gt;FeatureDomainModule -->
<g id="edge59" class="edge">
<title>AuthSaga&#45;&gt;FeatureDomainModule</title>
<path fill="none" stroke="black" d="M3429.29,-184.18C3429.29,-227.22 3429.29,-347 3429.29,-347 3429.29,-347 8773.44,-347 8773.44,-347 8773.44,-347 8773.44,-346.21 8773.44,-346.21"/>
<polygon fill="black" stroke="black" points="8776.94,-349.06 8773.44,-339.06 8769.94,-349.06 8776.94,-349.06"/>
</g>
<!-- AuthSaga&#45;&gt;MsxDomainModule -->
<g id="edge122" class="edge">
<title>AuthSaga&#45;&gt;MsxDomainModule</title>
<path fill="none" stroke="black" d="M3396.54,-187.07C3396.54,-196.46 3396.54,-206 3396.54,-206 3396.54,-206 9388.74,-206 9388.74,-206 9388.74,-206 9388.74,-216.87 9388.74,-216.87"/>
<polygon fill="black" stroke="black" points="9385.24,-216.87 9388.74,-226.87 9392.24,-216.87 9385.24,-216.87"/>
</g>
<!-- AuthSaga&#45;&gt;PermissionDomainModule -->
<g id="edge143" class="edge">
<title>AuthSaga&#45;&gt;PermissionDomainModule</title>
<path fill="none" stroke="black" d="M3440.2,-180.46C3440.2,-217.56 3440.2,-332 3440.2,-332 3440.2,-332 8331.61,-332 8331.61,-332"/>
<polygon fill="black" stroke="black" points="8331.61,-335.5 8341.61,-332 8331.61,-328.5 8331.61,-335.5"/>
</g>
<!-- AuthSaga&#45;&gt;PermissionQuerySideModule -->
<g id="edge151" class="edge">
<title>AuthSaga&#45;&gt;PermissionQuerySideModule</title>
<path fill="none" stroke="black" d="M3385.63,-186.2C3385.63,-197.04 3385.63,-209 3385.63,-209 3385.63,-209 9230.73,-209 9230.73,-209 9230.73,-209 9230.73,-216.58 9230.73,-216.58"/>
<polygon fill="black" stroke="black" points="9227.23,-216.58 9230.73,-226.58 9234.23,-216.58 9227.23,-216.58"/>
</g>
<!-- AuthSaga&#45;&gt;RoleDomainModule -->
<g id="edge161" class="edge">
<title>AuthSaga&#45;&gt;RoleDomainModule</title>
<path fill="none" stroke="black" d="M3374.71,-184.05C3374.71,-199.72 3374.71,-222 3374.71,-222 3374.71,-222 8534.21,-222 8534.21,-222 8534.21,-222 8534.21,-292.84 8534.21,-292.84"/>
<polygon fill="black" stroke="black" points="8530.71,-292.84 8534.21,-302.84 8537.71,-292.84 8530.71,-292.84"/>
</g>
<!-- AuthSaga&#45;&gt;UserDomainModule -->
<g id="edge183" class="edge">
<title>AuthSaga&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M3407.46,-187.25C3407.46,-252.17 3407.46,-468 3407.46,-468 3407.46,-468 8925.27,-468 8925.27,-468"/>
<polygon fill="black" stroke="black" points="8925.27,-471.5 8935.27,-468 8925.27,-464.5 8925.27,-471.5"/>
</g>
<!-- AuthSaga&#45;&gt;MsxQuerySideModule -->
<g id="edge130" class="edge">
<title>AuthSaga&#45;&gt;MsxQuerySideModule</title>
<path fill="none" stroke="black" d="M3363.8,-180.33C3363.8,-196.7 3363.8,-225 3363.8,-225 3363.8,-225 8215.57,-225 8215.57,-225 8215.57,-225 8215.57,-225.18 8215.57,-225.18"/>
<polygon fill="black" stroke="black" points="8212.07,-216.85 8215.57,-226.85 8219.07,-216.85 8212.07,-216.85"/>
</g>
<!-- AuthService -->
<g id="node22" class="node">
<title>AuthService</title>
<ellipse fill="#fdb462" stroke="black" cx="3276" cy="-169" rx="59.11" ry="18"/>
<text text-anchor="middle" x="3276" y="-164.8" font-family="Times,serif" font-size="14.00">AuthService</text>
</g>
<!-- AuthService&#45;&gt;AuthDomainModule -->
<g id="edge26" class="edge">
<title>AuthService&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M3276,-187.33C3276,-240.83 3276,-393 3276,-393 3276,-393 8331.71,-393 8331.71,-393"/>
<polygon fill="black" stroke="black" points="8331.71,-396.5 8341.71,-393 8331.71,-389.5 8331.71,-396.5"/>
</g>
<!-- CryptographerService -->
<g id="node23" class="node">
<title>CryptographerService</title>
<ellipse fill="#fdb462" stroke="black" cx="2559" cy="-169" rx="96.11" ry="18"/>
<text text-anchor="middle" x="2559" y="-164.8" font-family="Times,serif" font-size="14.00">CryptographerService</text>
</g>
<!-- CryptographerService&#45;&gt;AuthDomainModule -->
<g id="edge27" class="edge">
<title>CryptographerService&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M2591.1,-186.04C2591.1,-240.39 2591.1,-406 2591.1,-406 2591.1,-406 8331.67,-406 8331.67,-406"/>
<polygon fill="black" stroke="black" points="8331.67,-409.5 8341.67,-406 8331.67,-402.5 8331.67,-409.5"/>
</g>
<!-- CryptographerService&#45;&gt;UserDomainModule -->
<g id="edge184" class="edge">
<title>CryptographerService&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M2526.9,-186.22C2526.9,-250.52 2526.9,-473 2526.9,-473 2526.9,-473 8925.34,-473 8925.34,-473"/>
<polygon fill="black" stroke="black" points="8925.35,-476.5 8935.34,-473 8925.34,-469.5 8925.35,-476.5"/>
</g>
<!-- JwtStrategy -->
<g id="node24" class="node">
<title>JwtStrategy</title>
<ellipse fill="#fdb462" stroke="black" cx="2730" cy="-169" rx="56.76" ry="18"/>
<text text-anchor="middle" x="2730" y="-164.8" font-family="Times,serif" font-size="14.00">JwtStrategy</text>
</g>
<!-- JwtStrategy&#45;&gt;AuthDomainModule -->
<g id="edge28" class="edge">
<title>JwtStrategy&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M2730,-187.28C2730,-242.21 2730,-402 2730,-402 2730,-402 8331.77,-402 8331.77,-402"/>
<polygon fill="black" stroke="black" points="8331.77,-405.5 8341.77,-402 8331.77,-398.5 8331.77,-405.5"/>
</g>
<!-- LocalStrategy -->
<g id="node25" class="node">
<title>LocalStrategy</title>
<ellipse fill="#fdb462" stroke="black" cx="2870" cy="-169" rx="65.41" ry="18"/>
<text text-anchor="middle" x="2870" y="-164.8" font-family="Times,serif" font-size="14.00">LocalStrategy</text>
</g>
<!-- LocalStrategy&#45;&gt;AuthDomainModule -->
<g id="edge29" class="edge">
<title>LocalStrategy&#45;&gt;AuthDomainModule</title>
<path fill="none" stroke="black" d="M2870,-187.05C2870,-241.27 2870,-399 2870,-399 2870,-399 8331.46,-399 8331.46,-399"/>
<polygon fill="black" stroke="black" points="8331.46,-402.5 8341.46,-399 8331.46,-395.5 8331.46,-402.5"/>
</g>
<!-- QueryDatabaseModule -->
<g id="node26" class="node">
<title>QueryDatabaseModule</title>
<polygon fill="#8dd3c7" stroke="black" points="2154.83,-187 2151.83,-191 2130.83,-191 2127.83,-187 2009.17,-187 2009.17,-151 2154.83,-151 2154.83,-187"/>
<text text-anchor="middle" x="2082" y="-164.8" font-family="Times,serif" font-size="14.00">QueryDatabaseModule</text>
</g>
<!-- QueryDatabaseModule&#45;&gt;FeatureQuerySideModule -->
<g id="edge64" class="edge">
<title>QueryDatabaseModule&#45;&gt;FeatureQuerySideModule</title>
<path fill="none" stroke="black" d="M2123.86,-187.49C2123.86,-199.14 2123.86,-212 2123.86,-212 2123.86,-212 9044.1,-212 9044.1,-212 9044.1,-212 9044.1,-216.96 9044.1,-216.96"/>
<polygon fill="black" stroke="black" points="9040.6,-216.96 9044.1,-226.96 9047.6,-216.96 9040.6,-216.96"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;PermissionQuerySideModule -->
<g id="edge148" class="edge">
<title>QueryDatabaseModule&#45;&gt;PermissionQuerySideModule</title>
<path fill="none" stroke="black" d="M2131.63,-187.06C2131.63,-198.44 2131.63,-211 2131.63,-211 2131.63,-211 9200.45,-211 9200.45,-211 9200.45,-211 9200.45,-216.81 9200.45,-216.81"/>
<polygon fill="black" stroke="black" points="9196.95,-216.81 9200.45,-226.81 9203.95,-216.81 9196.95,-216.81"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;RoleQuerySideModule -->
<g id="edge166" class="edge">
<title>QueryDatabaseModule&#45;&gt;RoleQuerySideModule</title>
<path fill="none" stroke="black" d="M2116.1,-187.04C2116.1,-200.29 2116.1,-216 2116.1,-216 2116.1,-216 8815.24,-216 8815.24,-216 8815.24,-216 8815.24,-217.08 8815.24,-217.08"/>
<polygon fill="black" stroke="black" points="8811.74,-216.8 8815.24,-226.8 8818.74,-216.8 8811.74,-216.8"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;UserQuerySideModule -->
<g id="edge201" class="edge">
<title>QueryDatabaseModule&#45;&gt;UserQuerySideModule</title>
<path fill="none" stroke="black" d="M2147.15,-187.07C2147.15,-220.36 2147.15,-288 2147.15,-288 2147.15,-288 9208.05,-288 9208.05,-288 9208.05,-288 9208.05,-292.96 9208.05,-292.96"/>
<polygon fill="black" stroke="black" points="9204.55,-292.96 9208.05,-302.96 9211.55,-292.96 9204.55,-292.96"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;AuthExportModule -->
<g id="edge31" class="edge">
<title>QueryDatabaseModule&#45;&gt;AuthExportModule</title>
<path fill="none" stroke="black" d="M2045.68,-187.03C2045.68,-207.77 2045.68,-239 2045.68,-239 2045.68,-239 1934.7,-239 1934.7,-239"/>
<polygon fill="black" stroke="black" points="1934.7,-235.5 1924.7,-239 1934.7,-242.5 1934.7,-235.5"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;AuthQuerySideModule -->
<g id="edge40" class="edge">
<title>QueryDatabaseModule&#45;&gt;AuthQuerySideModule</title>
<path fill="none" stroke="black" d="M2139.39,-187.08C2139.39,-188.85 2139.39,-190 2139.39,-190 2139.39,-190 12896.34,-190 12896.34,-190 12896.34,-190 12896.34,-216.99 12896.34,-216.99"/>
<polygon fill="black" stroke="black" points="12892.84,-216.99 12896.34,-226.99 12899.84,-216.99 12892.84,-216.99"/>
</g>
<!-- CodeGenerateModule -->
<g id="node39" class="node">
<title>CodeGenerateModule</title>
<polygon fill="#8dd3c7" stroke="black" points="484.62,-263 481.62,-267 460.62,-267 457.62,-263 345.38,-263 345.38,-227 484.62,-227 484.62,-263"/>
<text text-anchor="middle" x="415" y="-240.8" font-family="Times,serif" font-size="14.00">CodeGenerateModule</text>
</g>
<!-- QueryDatabaseModule&#45;&gt;CodeGenerateModule -->
<g id="edge46" class="edge">
<title>QueryDatabaseModule&#45;&gt;CodeGenerateModule</title>
<path fill="none" stroke="black" d="M2027.38,-187.21C2027.38,-192.55 2027.38,-197 2027.38,-197 2027.38,-197 438.27,-197 438.27,-197 438.27,-197 438.27,-216.85 438.27,-216.85"/>
<polygon fill="black" stroke="black" points="434.77,-216.85 438.27,-226.85 441.77,-216.85 434.77,-216.85"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;MsxQuerySideModule -->
<g id="edge127" class="edge">
<title>QueryDatabaseModule&#45;&gt;MsxQuerySideModule</title>
<path fill="none" stroke="black" d="M2108.34,-187.04C2108.34,-203.73 2108.34,-226 2108.34,-226 2108.34,-226 8186.71,-226 8186.71,-226 8186.71,-226 8186.71,-226.08 8186.71,-226.08"/>
<polygon fill="black" stroke="black" points="8183.21,-216.82 8186.71,-226.82 8190.21,-216.82 8183.21,-216.82"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;MasterQuerySideModule -->
<g id="edge89" class="edge">
<title>QueryDatabaseModule&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M2063.98,-187.32C2063.98,-218.13 2063.98,-277 2063.98,-277 2063.98,-277 1161.2,-277 1161.2,-277 1161.2,-277 1161.2,-292.85 1161.2,-292.85"/>
<polygon fill="black" stroke="black" points="1157.7,-292.85 1161.2,-302.85 1164.7,-292.85 1157.7,-292.85"/>
</g>
<!-- QueryDatabaseModule&#45;&gt;UserExportModule -->
<g id="edge192" class="edge">
<title>QueryDatabaseModule&#45;&gt;UserExportModule</title>
<path fill="none" stroke="black" d="M2082.28,-187.49C2082.28,-218.59 2082.28,-278 2082.28,-278 2082.28,-278 1281.22,-278 1281.22,-278 1281.22,-278 1281.22,-292.51 1281.22,-292.51"/>
<polygon fill="black" stroke="black" points="1277.72,-292.51 1281.22,-302.51 1284.72,-292.51 1277.72,-292.51"/>
</g>
<!-- AuthExportModule  -->
<g id="node28" class="node">
<title>AuthExportModule </title>
<polygon fill="#fb8072" stroke="black" points="2100.65,-339 1971.35,-339 1971.35,-303 2100.65,-303 2100.65,-339"/>
<text text-anchor="middle" x="2036" y="-316.8" font-family="Times,serif" font-size="14.00">AuthExportModule </text>
</g>
<!-- AuthExportModule&#45;&gt;AuthExportModule  -->
<g id="edge32" class="edge">
<title>AuthExportModule&#45;&gt;AuthExportModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1924.91,-251C1952.3,-251 1978.28,-251 1978.28,-251 1978.28,-251 1978.28,-292.97 1978.28,-292.97"/>
<polygon fill="black" stroke="black" points="1974.78,-292.97 1978.28,-302.97 1981.78,-292.97 1974.78,-292.97"/>
</g>
<!-- AuthQueryExportRepository  -->
<g id="node29" class="node">
<title>AuthQueryExportRepository </title>
<polygon fill="#fb8072" stroke="black" points="1953.02,-339 1770.98,-339 1770.98,-303 1953.02,-303 1953.02,-339"/>
<text text-anchor="middle" x="1862" y="-316.8" font-family="Times,serif" font-size="14.00">AuthQueryExportRepository </text>
</g>
<!-- AuthExportModule&#45;&gt;AuthQueryExportRepository  -->
<g id="edge33" class="edge">
<title>AuthExportModule&#45;&gt;AuthQueryExportRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1862,-263.01C1862,-263.01 1862,-292.85 1862,-292.85"/>
<polygon fill="black" stroke="black" points="1858.5,-292.85 1862,-302.85 1865.5,-292.85 1858.5,-292.85"/>
</g>
<!-- AuthQueryExportService  -->
<g id="node30" class="node">
<title>AuthQueryExportService </title>
<polygon fill="#fb8072" stroke="black" points="1753.06,-339 1590.94,-339 1590.94,-303 1753.06,-303 1753.06,-339"/>
<text text-anchor="middle" x="1672" y="-316.8" font-family="Times,serif" font-size="14.00">AuthQueryExportService </text>
</g>
<!-- AuthExportModule&#45;&gt;AuthQueryExportService  -->
<g id="edge34" class="edge">
<title>AuthExportModule&#45;&gt;AuthQueryExportService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1799.28,-254C1771.97,-254 1746.07,-254 1746.07,-254 1746.07,-254 1746.07,-292.69 1746.07,-292.69"/>
<polygon fill="black" stroke="black" points="1742.57,-292.69 1746.07,-302.69 1749.57,-292.69 1742.57,-292.69"/>
</g>
<!-- AuthExportModule&#45;&gt;UserExportModule -->
<g id="edge187" class="edge">
<title>AuthExportModule&#45;&gt;UserExportModule</title>
<path fill="none" stroke="black" d="M1799.25,-245C1717.65,-245 1585.44,-245 1585.44,-245 1585.44,-245 1585.44,-332 1585.44,-332 1585.44,-332 1316.23,-332 1316.23,-332"/>
<polygon fill="black" stroke="black" points="1316.23,-328.5 1306.23,-332 1316.23,-335.5 1316.23,-328.5"/>
</g>
<!-- AuthQueryExportRepository -->
<g id="node31" class="node">
<title>AuthQueryExportRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="1862" cy="-169" rx="123.28" ry="18"/>
<text text-anchor="middle" x="1862" y="-164.8" font-family="Times,serif" font-size="14.00">AuthQueryExportRepository</text>
</g>
<!-- AuthQueryExportRepository&#45;&gt;AuthExportModule -->
<g id="edge35" class="edge">
<title>AuthQueryExportRepository&#45;&gt;AuthExportModule</title>
<path fill="none" stroke="black" d="M1841.06,-187.01C1841.06,-187.01 1841.06,-216.85 1841.06,-216.85"/>
<polygon fill="black" stroke="black" points="1837.56,-216.85 1841.06,-226.85 1844.56,-216.85 1837.56,-216.85"/>
</g>
<!-- AuthQueryExportService -->
<g id="node32" class="node">
<title>AuthQueryExportService</title>
<ellipse fill="#fdb462" stroke="black" cx="1611" cy="-169" rx="109.93" ry="18"/>
<text text-anchor="middle" x="1611" y="-164.8" font-family="Times,serif" font-size="14.00">AuthQueryExportService</text>
</g>
<!-- AuthQueryExportService&#45;&gt;AuthExportModule -->
<g id="edge36" class="edge">
<title>AuthQueryExportService&#45;&gt;AuthExportModule</title>
<path fill="none" stroke="black" d="M1655.72,-185.56C1655.72,-205.4 1655.72,-236 1655.72,-236 1655.72,-236 1789.27,-236 1789.27,-236"/>
<polygon fill="black" stroke="black" points="1789.27,-239.5 1799.27,-236 1789.27,-232.5 1789.27,-239.5"/>
</g>
<!-- AuthQueryRepository  -->
<g id="node34" class="node">
<title>AuthQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="13178.92,-339 13035.08,-339 13035.08,-303 13178.92,-303 13178.92,-339"/>
<text text-anchor="middle" x="13107" y="-316.8" font-family="Times,serif" font-size="14.00">AuthQueryRepository </text>
</g>
<!-- AuthQuerySideModule&#45;&gt;AuthQueryRepository  -->
<g id="edge41" class="edge">
<title>AuthQuerySideModule&#45;&gt;AuthQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M13022.97,-263.01C13022.97,-285.49 13022.97,-321 13022.97,-321 13022.97,-321 13025.01,-321 13025.01,-321"/>
<polygon fill="black" stroke="black" points="13025.01,-324.5 13035.01,-321 13025.01,-317.5 13025.01,-324.5"/>
</g>
<!-- AuthQueryService  -->
<g id="node35" class="node">
<title>AuthQueryService </title>
<polygon fill="#fb8072" stroke="black" points="13017.46,-339 12892.54,-339 12892.54,-303 13017.46,-303 13017.46,-339"/>
<text text-anchor="middle" x="12955" y="-316.8" font-family="Times,serif" font-size="14.00">AuthQueryService </text>
</g>
<!-- AuthQuerySideModule&#45;&gt;AuthQueryService  -->
<g id="edge42" class="edge">
<title>AuthQuerySideModule&#45;&gt;AuthQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M12955,-263.01C12955,-263.01 12955,-292.85 12955,-292.85"/>
<polygon fill="black" stroke="black" points="12951.5,-292.85 12955,-302.85 12958.5,-292.85 12951.5,-292.85"/>
</g>
<!-- AuthQuerySideModule  -->
<g id="node36" class="node">
<title>AuthQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="12874.42,-339 12723.58,-339 12723.58,-303 12874.42,-303 12874.42,-339"/>
<text text-anchor="middle" x="12799" y="-316.8" font-family="Times,serif" font-size="14.00">AuthQuerySideModule </text>
</g>
<!-- AuthQuerySideModule&#45;&gt;AuthQuerySideModule  -->
<g id="edge43" class="edge">
<title>AuthQuerySideModule&#45;&gt;AuthQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M12887.03,-263.01C12887.03,-285.49 12887.03,-321 12887.03,-321 12887.03,-321 12884.42,-321 12884.42,-321"/>
<polygon fill="black" stroke="black" points="12884.42,-317.5 12874.42,-321 12884.42,-324.5 12884.42,-317.5"/>
</g>
<!-- AuthQueryRepository -->
<g id="node37" class="node">
<title>AuthQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="13005" cy="-169" rx="96.71" ry="18"/>
<text text-anchor="middle" x="13005" y="-164.8" font-family="Times,serif" font-size="14.00">AuthQueryRepository</text>
</g>
<!-- AuthQueryRepository&#45;&gt;AuthQuerySideModule -->
<g id="edge44" class="edge">
<title>AuthQueryRepository&#45;&gt;AuthQuerySideModule</title>
<path fill="none" stroke="black" d="M12968.43,-185.87C12968.43,-185.87 12968.43,-216.85 12968.43,-216.85"/>
<polygon fill="black" stroke="black" points="12964.93,-216.85 12968.43,-226.85 12971.93,-216.85 12964.93,-216.85"/>
</g>
<!-- AuthQueryService -->
<g id="node38" class="node">
<title>AuthQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="12807" cy="-169" rx="83.37" ry="18"/>
<text text-anchor="middle" x="12807" y="-164.8" font-family="Times,serif" font-size="14.00">AuthQueryService</text>
</g>
<!-- AuthQueryService&#45;&gt;AuthQuerySideModule -->
<g id="edge45" class="edge">
<title>AuthQueryService&#45;&gt;AuthQuerySideModule</title>
<path fill="none" stroke="black" d="M12888.15,-173.53C12888.15,-173.53 12888.15,-216.7 12888.15,-216.7"/>
<polygon fill="black" stroke="black" points="12884.65,-216.7 12888.15,-226.7 12891.65,-216.7 12884.65,-216.7"/>
</g>
<!-- CodeGenerateModule&#45;&gt;UserDomainModule -->
<g id="edge173" class="edge">
<title>CodeGenerateModule&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M356.69,-263.28C356.69,-318.21 356.69,-478 356.69,-478 356.69,-478 8925.16,-478 8925.16,-478"/>
<polygon fill="black" stroke="black" points="8925.16,-481.5 8935.16,-478 8925.16,-474.5 8925.16,-481.5"/>
</g>
<!-- CodeGenerateModule  -->
<g id="node40" class="node">
<title>CodeGenerateModule </title>
<polygon fill="#fb8072" stroke="black" points="505.62,-339 362.38,-339 362.38,-303 505.62,-303 505.62,-339"/>
<text text-anchor="middle" x="434" y="-316.8" font-family="Times,serif" font-size="14.00">CodeGenerateModule </text>
</g>
<!-- CodeGenerateModule&#45;&gt;CodeGenerateModule  -->
<g id="edge47" class="edge">
<title>CodeGenerateModule&#45;&gt;CodeGenerateModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M403.23,-263.01C403.23,-263.01 403.23,-292.85 403.23,-292.85"/>
<polygon fill="black" stroke="black" points="399.73,-292.85 403.23,-302.85 406.73,-292.85 399.73,-292.85"/>
</g>
<!-- CodeGenerateRepository  -->
<g id="node41" class="node">
<title>CodeGenerateRepository </title>
<polygon fill="#fb8072" stroke="black" points="344.51,-339 183.49,-339 183.49,-303 344.51,-303 344.51,-339"/>
<text text-anchor="middle" x="264" y="-316.8" font-family="Times,serif" font-size="14.00">CodeGenerateRepository </text>
</g>
<!-- CodeGenerateModule&#45;&gt;CodeGenerateRepository  -->
<g id="edge48" class="edge">
<title>CodeGenerateModule&#45;&gt;CodeGenerateRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M350.94,-263.01C350.94,-285.49 350.94,-321 350.94,-321 350.94,-321 350.32,-321 350.32,-321"/>
<polygon fill="black" stroke="black" points="354.69,-317.5 344.69,-321 354.69,-324.5 354.69,-317.5"/>
</g>
<!-- CodeGenerateService  -->
<g id="node42" class="node">
<title>CodeGenerateService </title>
<polygon fill="#fb8072" stroke="black" points="165.55,-339 24.45,-339 24.45,-303 165.55,-303 165.55,-339"/>
<text text-anchor="middle" x="95" y="-316.8" font-family="Times,serif" font-size="14.00">CodeGenerateService </text>
</g>
<!-- CodeGenerateModule&#45;&gt;CodeGenerateService  -->
<g id="edge49" class="edge">
<title>CodeGenerateModule&#45;&gt;CodeGenerateService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M345.38,-251C258.94,-251 122.78,-251 122.78,-251 122.78,-251 122.78,-292.97 122.78,-292.97"/>
<polygon fill="black" stroke="black" points="119.28,-292.97 122.78,-302.97 126.28,-292.97 119.28,-292.97"/>
</g>
<!-- CodeGenerateModule&#45;&gt;UserExportModule -->
<g id="edge188" class="edge">
<title>CodeGenerateModule&#45;&gt;UserExportModule</title>
<path fill="none" stroke="black" d="M444.02,-263.03C444.02,-279.4 444.02,-301 444.02,-301 444.02,-301 1206.78,-301 1206.78,-301 1206.78,-301 1206.78,-301.18 1206.78,-301.18"/>
<polygon fill="black" stroke="black" points="1203.28,-292.85 1206.78,-302.85 1210.28,-292.85 1203.28,-292.85"/>
</g>
<!-- CodeGenerateRepository -->
<g id="node43" class="node">
<title>CodeGenerateRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="397" cy="-169" rx="108.79" ry="18"/>
<text text-anchor="middle" x="397" y="-164.8" font-family="Times,serif" font-size="14.00">CodeGenerateRepository</text>
</g>
<!-- CodeGenerateRepository&#45;&gt;CodeGenerateModule -->
<g id="edge50" class="edge">
<title>CodeGenerateRepository&#45;&gt;CodeGenerateModule</title>
<path fill="none" stroke="black" d="M391.73,-187.01C391.73,-187.01 391.73,-216.85 391.73,-216.85"/>
<polygon fill="black" stroke="black" points="388.23,-216.85 391.73,-226.85 395.23,-216.85 388.23,-216.85"/>
</g>
<!-- CodeGenerateService -->
<g id="node44" class="node">
<title>CodeGenerateService</title>
<ellipse fill="#fdb462" stroke="black" cx="175" cy="-169" rx="95.44" ry="18"/>
<text text-anchor="middle" x="175" y="-164.8" font-family="Times,serif" font-size="14.00">CodeGenerateService</text>
</g>
<!-- CodeGenerateService&#45;&gt;CodeGenerateModule -->
<g id="edge51" class="edge">
<title>CodeGenerateService&#45;&gt;CodeGenerateModule</title>
<path fill="none" stroke="black" d="M226.86,-184.19C226.86,-204.78 226.86,-239 226.86,-239 226.86,-239 335.21,-239 335.21,-239"/>
<polygon fill="black" stroke="black" points="335.21,-242.5 345.21,-239 335.21,-235.5 335.21,-242.5"/>
</g>
<!-- MsxQuerySideModule&#45;&gt;FeatureDomainModule -->
<g id="edge58" class="edge">
<title>MsxQuerySideModule&#45;&gt;FeatureDomainModule</title>
<path fill="none" stroke="black" d="M8199.08,-263.19C8199.08,-278.18 8199.08,-297 8199.08,-297 8199.08,-297 8715.34,-297 8715.34,-297 8715.34,-297 8715.34,-297.58 8715.34,-297.58"/>
<polygon fill="black" stroke="black" points="8711.84,-292.81 8715.34,-302.81 8718.84,-292.81 8711.84,-292.81"/>
</g>
<!-- MsxQuerySideModule&#45;&gt;FeatureQuerySideModule -->
<g id="edge63" class="edge">
<title>MsxQuerySideModule&#45;&gt;FeatureQuerySideModule</title>
<path fill="none" stroke="black" d="M8281.54,-263.19C8281.54,-271.28 8281.54,-279 8281.54,-279 8281.54,-279 9003.01,-279 9003.01,-279 9003.01,-279 9003.01,-273.19 9003.01,-273.19"/>
<polygon fill="black" stroke="black" points="9006.51,-273.19 9003.01,-263.19 8999.51,-273.19 9006.51,-273.19"/>
</g>
<!-- MsxQuerySideModule&#45;&gt;MsxDomainModule -->
<g id="edge121" class="edge">
<title>MsxQuerySideModule&#45;&gt;MsxDomainModule</title>
<path fill="none" stroke="black" d="M8260.92,-263.32C8260.92,-271.79 8260.92,-280 8260.92,-280 8260.92,-280 9435,-280 9435,-280 9435,-280 9435,-273.32 9435,-273.32"/>
<polygon fill="black" stroke="black" points="9438.5,-273.32 9435,-263.32 9431.5,-273.32 9438.5,-273.32"/>
</g>
<!-- MsxQueryRepository  -->
<g id="node88" class="node">
<title>MsxQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="10028.31,-339 9887.69,-339 9887.69,-303 10028.31,-303 10028.31,-339"/>
<text text-anchor="middle" x="9958" y="-316.8" font-family="Times,serif" font-size="14.00">MsxQueryRepository </text>
</g>
<!-- MsxQuerySideModule&#45;&gt;MsxQueryRepository  -->
<g id="edge128" class="edge">
<title>MsxQuerySideModule&#45;&gt;MsxQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8219.69,-263.07C8219.69,-272.46 8219.69,-282 8219.69,-282 8219.69,-282 9958,-282 9958,-282 9958,-282 9958,-292.87 9958,-292.87"/>
<polygon fill="black" stroke="black" points="9954.5,-292.87 9958,-302.87 9961.5,-292.87 9954.5,-292.87"/>
</g>
<!-- MsxQuerySideModule  -->
<g id="node89" class="node">
<title>MsxQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="10193.81,-339 10046.19,-339 10046.19,-303 10193.81,-303 10193.81,-339"/>
<text text-anchor="middle" x="10120" y="-316.8" font-family="Times,serif" font-size="14.00">MsxQuerySideModule </text>
</g>
<!-- MsxQuerySideModule&#45;&gt;MsxQuerySideModule  -->
<g id="edge129" class="edge">
<title>MsxQuerySideModule&#45;&gt;MsxQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8240.31,-263.42C8240.31,-272.28 8240.31,-281 8240.31,-281 8240.31,-281 10120,-281 10120,-281 10120,-281 10120,-292.87 10120,-292.87"/>
<polygon fill="black" stroke="black" points="10116.5,-292.87 10120,-302.87 10123.5,-292.87 10116.5,-292.87"/>
</g>
<!-- FeatureEventStreamRepository -->
<g id="node48" class="node">
<title>FeatureEventStreamRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="7846" cy="-245" rx="132.5" ry="18"/>
<text text-anchor="middle" x="7846" y="-240.8" font-family="Times,serif" font-size="14.00">FeatureEventStreamRepository</text>
</g>
<!-- FeatureEventStreamRepository&#45;&gt;FeatureDomainModule -->
<g id="edge60" class="edge">
<title>FeatureEventStreamRepository&#45;&gt;FeatureDomainModule</title>
<path fill="none" stroke="black" d="M7846,-263.28C7846,-278.95 7846,-299 7846,-299 7846,-299 8688.9,-299 8688.9,-299 8688.9,-299 8688.9,-299.38 8688.9,-299.38"/>
<polygon fill="black" stroke="black" points="8685.4,-292.83 8688.9,-302.83 8692.4,-292.83 8685.4,-292.83"/>
</g>
<!-- FeatureService -->
<g id="node49" class="node">
<title>FeatureService</title>
<ellipse fill="#fdb462" stroke="black" cx="8065" cy="-245" rx="68.89" ry="18"/>
<text text-anchor="middle" x="8065" y="-240.8" font-family="Times,serif" font-size="14.00">FeatureService</text>
</g>
<!-- FeatureService&#45;&gt;FeatureDomainModule -->
<g id="edge61" class="edge">
<title>FeatureService&#45;&gt;FeatureDomainModule</title>
<path fill="none" stroke="black" d="M8042.02,-262.06C8042.02,-277.55 8042.02,-298 8042.02,-298 8042.02,-298 8702.12,-298 8702.12,-298 8702.12,-298 8702.12,-298.49 8702.12,-298.49"/>
<polygon fill="black" stroke="black" points="8698.62,-292.88 8702.12,-302.88 8705.62,-292.88 8698.62,-292.88"/>
</g>
<!-- FeatureService&#45;&gt;FeatureQuerySideModule -->
<g id="edge69" class="edge">
<title>FeatureService&#45;&gt;FeatureQuerySideModule</title>
<path fill="none" stroke="black" d="M8087.98,-262.22C8087.98,-272.67 8087.98,-284 8087.98,-284 8087.98,-284 9015.71,-284 9015.71,-284 9015.71,-284 9015.71,-273.13 9015.71,-273.13"/>
<polygon fill="black" stroke="black" points="9019.21,-273.13 9015.71,-263.13 9012.21,-273.13 9019.21,-273.13"/>
</g>
<!-- FeatureQueryRepository -->
<g id="node53" class="node">
<title>FeatureQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="10341" cy="-321" rx="106.5" ry="18"/>
<text text-anchor="middle" x="10341" y="-316.8" font-family="Times,serif" font-size="14.00">FeatureQueryRepository</text>
</g>
<!-- FeatureQueryRepository&#45;&gt;FeatureQuerySideModule -->
<g id="edge68" class="edge">
<title>FeatureQueryRepository&#45;&gt;FeatureQuerySideModule</title>
<path fill="none" stroke="black" d="M10341,-302.96C10341,-290.44 10341,-276 10341,-276 10341,-276 9145.25,-276 9145.25,-276 9145.25,-276 9145.25,-273.03 9145.25,-273.03"/>
<polygon fill="black" stroke="black" points="9148.75,-273.03 9145.25,-263.03 9141.75,-273.03 9148.75,-273.03"/>
</g>
<!-- MasterQuerySideModule&#45;&gt;ListenerModule -->
<g id="edge74" class="edge">
<title>MasterQuerySideModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M1118.92,-339.04C1118.92,-352.29 1118.92,-368 1118.92,-368 1118.92,-368 8911.21,-368 8911.21,-368 8911.21,-368 8911.21,-512.81 8911.21,-512.81"/>
<polygon fill="black" stroke="black" points="8907.71,-512.81 8911.21,-522.81 8914.71,-512.81 8907.71,-512.81"/>
</g>
<!-- MasterQueryRepository  -->
<g id="node59" class="node">
<title>MasterQueryRepository </title>
<polygon fill="#fb8072" stroke="black" points="1901.29,-415 1746.71,-415 1746.71,-379 1901.29,-379 1901.29,-415"/>
<text text-anchor="middle" x="1824" y="-392.8" font-family="Times,serif" font-size="14.00">MasterQueryRepository </text>
</g>
<!-- MasterQuerySideModule&#45;&gt;MasterQueryRepository  -->
<g id="edge91" class="edge">
<title>MasterQuerySideModule&#45;&gt;MasterQueryRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1096.31,-339.25C1096.31,-353.18 1096.31,-370 1096.31,-370 1096.31,-370 1762.13,-370 1762.13,-370 1762.13,-370 1762.13,-370.88 1762.13,-370.88"/>
<polygon fill="black" stroke="black" points="1758.64,-368.84 1762.13,-378.84 1765.64,-368.84 1758.64,-368.84"/>
</g>
<!-- MasterQueryService  -->
<g id="node60" class="node">
<title>MasterQueryService </title>
<polygon fill="#fb8072" stroke="black" points="1728.83,-415 1593.17,-415 1593.17,-379 1728.83,-379 1728.83,-415"/>
<text text-anchor="middle" x="1661" y="-392.8" font-family="Times,serif" font-size="14.00">MasterQueryService </text>
</g>
<!-- MasterQuerySideModule&#45;&gt;MasterQueryService  -->
<g id="edge92" class="edge">
<title>MasterQuerySideModule&#45;&gt;MasterQueryService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1073.69,-339.12C1073.69,-353.78 1073.69,-372 1073.69,-372 1073.69,-372 1661,-372 1661,-372 1661,-372 1661,-372.68 1661,-372.68"/>
<polygon fill="black" stroke="black" points="1657.5,-368.83 1661,-378.83 1664.5,-368.83 1657.5,-368.83"/>
</g>
<!-- MasterQuerySideModule  -->
<g id="node61" class="node">
<title>MasterQuerySideModule </title>
<polygon fill="#fb8072" stroke="black" points="1574.79,-415 1413.21,-415 1413.21,-379 1574.79,-379 1574.79,-415"/>
<text text-anchor="middle" x="1494" y="-392.8" font-family="Times,serif" font-size="14.00">MasterQuerySideModule </text>
</g>
<!-- MasterQuerySideModule&#45;&gt;MasterQuerySideModule  -->
<g id="edge93" class="edge">
<title>MasterQuerySideModule&#45;&gt;MasterQuerySideModule </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1051.08,-339.28C1051.08,-354.95 1051.08,-375 1051.08,-375 1051.08,-375 1437.05,-375 1437.05,-375 1437.05,-375 1437.05,-375.38 1437.05,-375.38"/>
<polygon fill="black" stroke="black" points="1433.55,-368.83 1437.05,-378.83 1440.55,-368.83 1433.55,-368.83"/>
</g>
<!-- UserExportModule&#45;&gt;ListenerModule -->
<g id="edge79" class="edge">
<title>UserExportModule&#45;&gt;ListenerModule</title>
<path fill="none" stroke="black" d="M1198.21,-339.35C1198.21,-393.69 1198.21,-550 1198.21,-550 1198.21,-550 8895.09,-550 8895.09,-550"/>
<polygon fill="black" stroke="black" points="8895.09,-553.5 8905.09,-550 8895.09,-546.5 8895.09,-553.5"/>
</g>
<!-- UserExportModule&#45;&gt;PatchDataModule -->
<g id="edge135" class="edge">
<title>UserExportModule&#45;&gt;PatchDataModule</title>
<path fill="none" stroke="black" d="M1246.66,-339.14C1246.66,-345.46 1246.66,-351 1246.66,-351 1246.66,-351 13213.43,-351 13213.43,-351 13213.43,-351 13213.43,-368.82 13213.43,-368.82"/>
<polygon fill="black" stroke="black" points="13209.93,-368.82 13213.43,-378.82 13216.93,-368.82 13209.93,-368.82"/>
</g>
<!-- UserExportRepository  -->
<g id="node111" class="node">
<title>UserExportRepository </title>
<polygon fill="#fb8072" stroke="black" points="1316.97,-263 1171.03,-263 1171.03,-227 1316.97,-227 1316.97,-263"/>
<text text-anchor="middle" x="1244" y="-240.8" font-family="Times,serif" font-size="14.00">UserExportRepository </text>
</g>
<!-- UserExportModule&#45;&gt;UserExportRepository  -->
<g id="edge194" class="edge">
<title>UserExportModule&#45;&gt;UserExportRepository </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1256.41,-302.99C1256.41,-302.99 1256.41,-273.15 1256.41,-273.15"/>
<polygon fill="black" stroke="black" points="1259.91,-273.15 1256.41,-263.15 1252.91,-273.15 1259.91,-273.15"/>
</g>
<!-- UserExportService  -->
<g id="node112" class="node">
<title>UserExportService </title>
<polygon fill="#fb8072" stroke="black" points="1461.01,-263 1334.99,-263 1334.99,-227 1461.01,-227 1461.01,-263"/>
<text text-anchor="middle" x="1398" y="-240.8" font-family="Times,serif" font-size="14.00">UserExportService </text>
</g>
<!-- UserExportModule&#45;&gt;UserExportService  -->
<g id="edge195" class="edge">
<title>UserExportModule&#45;&gt;UserExportService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1306.09,-311C1334.51,-311 1361.96,-311 1361.96,-311 1361.96,-311 1361.96,-273.03 1361.96,-273.03"/>
<polygon fill="black" stroke="black" points="1365.46,-273.03 1361.96,-263.03 1358.46,-273.03 1365.46,-273.03"/>
</g>
<!-- MsxLoggerService -->
<g id="node58" class="node">
<title>MsxLoggerService</title>
<ellipse fill="#fdb462" stroke="black" cx="11043" cy="-245" rx="85.09" ry="18"/>
<text text-anchor="middle" x="11043" y="-240.8" font-family="Times,serif" font-size="14.00">MsxLoggerService</text>
</g>
<!-- MsxLoggerService&#45;&gt;LoggerModule -->
<g id="edge85" class="edge">
<title>MsxLoggerService&#45;&gt;LoggerModule</title>
<path fill="none" stroke="black" d="M11043,-226.97C11043,-202.02 11043,-160 11043,-160 11043,-160 8682.99,-160 8682.99,-160"/>
<polygon fill="black" stroke="black" points="8682.99,-156.5 8672.99,-160 8682.99,-163.5 8682.99,-156.5"/>
</g>
<!-- MasterQueryRepository -->
<g id="node62" class="node">
<title>MasterQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="1085" cy="-397" rx="104.21" ry="18"/>
<text text-anchor="middle" x="1085" y="-392.8" font-family="Times,serif" font-size="14.00">MasterQueryRepository</text>
</g>
<!-- MasterQueryRepository&#45;&gt;MasterQuerySideModule -->
<g id="edge94" class="edge">
<title>MasterQueryRepository&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M1028.47,-381.62C1028.47,-381.62 1028.47,-349.03 1028.47,-349.03"/>
<polygon fill="black" stroke="black" points="1031.97,-349.03 1028.47,-339.03 1024.97,-349.03 1031.97,-349.03"/>
</g>
<!-- MasterQueryService -->
<g id="node63" class="node">
<title>MasterQueryService</title>
<ellipse fill="#fdb462" stroke="black" cx="1298" cy="-397" rx="90.87" ry="18"/>
<text text-anchor="middle" x="1298" y="-392.8" font-family="Times,serif" font-size="14.00">MasterQueryService</text>
</g>
<!-- MasterQueryService&#45;&gt;MasterQuerySideModule -->
<g id="edge95" class="edge">
<title>MasterQueryService&#45;&gt;MasterQuerySideModule</title>
<path fill="none" stroke="black" d="M1226.86,-385.6C1226.86,-381.11 1226.86,-377 1226.86,-377 1226.86,-377 1141.53,-377 1141.53,-377 1141.53,-377 1141.53,-349.03 1141.53,-349.03"/>
<polygon fill="black" stroke="black" points="1145.03,-349.03 1141.53,-339.03 1138.03,-349.03 1145.03,-349.03"/>
</g>
<!-- CareClient -->
<g id="node75" class="node">
<title>CareClient</title>
<ellipse fill="#fdb462" stroke="black" cx="6418" cy="-169" rx="52.74" ry="18"/>
<text text-anchor="middle" x="6418" y="-164.8" font-family="Times,serif" font-size="14.00">CareClient</text>
</g>
<!-- CareClient&#45;&gt;MgsSenderModule -->
<g id="edge107" class="edge">
<title>CareClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M6418,-150.97C6418,-131.42 6418,-103 6418,-103 6418,-103 8332.11,-103 8332.11,-103"/>
<polygon fill="black" stroke="black" points="8332.11,-106.5 8342.11,-103 8332.11,-99.5 8332.11,-106.5"/>
</g>
<!-- CustomerClient -->
<g id="node76" class="node">
<title>CustomerClient</title>
<ellipse fill="#fdb462" stroke="black" cx="6275" cy="-169" rx="72.39" ry="18"/>
<text text-anchor="middle" x="6275" y="-164.8" font-family="Times,serif" font-size="14.00">CustomerClient</text>
</g>
<!-- CustomerClient&#45;&gt;MgsSenderModule -->
<g id="edge108" class="edge">
<title>CustomerClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M6275,-150.69C6275,-130.85 6275,-102 6275,-102 6275,-102 8332.29,-102 8332.29,-102"/>
<polygon fill="black" stroke="black" points="8332.29,-105.5 8342.29,-102 8332.29,-98.5 8332.29,-105.5"/>
</g>
<!-- EmployeeClient -->
<g id="node77" class="node">
<title>EmployeeClient</title>
<ellipse fill="#fdb462" stroke="black" cx="6111" cy="-169" rx="73.59" ry="18"/>
<text text-anchor="middle" x="6111" y="-164.8" font-family="Times,serif" font-size="14.00">EmployeeClient</text>
</g>
<!-- EmployeeClient&#45;&gt;MgsSenderModule -->
<g id="edge109" class="edge">
<title>EmployeeClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M6111,-150.87C6111,-130.43 6111,-100 6111,-100 6111,-100 8332.07,-100 8332.07,-100"/>
<polygon fill="black" stroke="black" points="8332.07,-103.5 8342.07,-100 8332.07,-96.5 8332.07,-103.5"/>
</g>
<!-- LoggerClient -->
<g id="node78" class="node">
<title>LoggerClient</title>
<ellipse fill="#fdb462" stroke="black" cx="5957" cy="-169" rx="62.56" ry="18"/>
<text text-anchor="middle" x="5957" y="-164.8" font-family="Times,serif" font-size="14.00">LoggerClient</text>
</g>
<!-- LoggerClient&#45;&gt;MgsSenderModule -->
<g id="edge110" class="edge">
<title>LoggerClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M5957,-150.97C5957,-130.23 5957,-99 5957,-99 5957,-99 8332.29,-99 8332.29,-99"/>
<polygon fill="black" stroke="black" points="8332.29,-102.5 8342.29,-99 8332.29,-95.5 8332.29,-102.5"/>
</g>
<!-- MailerClient -->
<g id="node79" class="node">
<title>MailerClient</title>
<ellipse fill="#fdb462" stroke="black" cx="5816" cy="-169" rx="60.27" ry="18"/>
<text text-anchor="middle" x="5816" y="-164.8" font-family="Times,serif" font-size="14.00">MailerClient</text>
</g>
<!-- MailerClient&#45;&gt;MgsSenderModule -->
<g id="edge111" class="edge">
<title>MailerClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M5816,-150.72C5816,-129.68 5816,-98 5816,-98 5816,-98 8332.26,-98 8332.26,-98"/>
<polygon fill="black" stroke="black" points="8332.26,-101.5 8342.26,-98 8332.26,-94.5 8332.26,-101.5"/>
</g>
<!-- MsgSenderService -->
<g id="node80" class="node">
<title>MsgSenderService</title>
<ellipse fill="#fdb462" stroke="black" cx="5654" cy="-169" rx="83.95" ry="18"/>
<text text-anchor="middle" x="5654" y="-164.8" font-family="Times,serif" font-size="14.00">MsgSenderService</text>
</g>
<!-- MsgSenderService&#45;&gt;MgsSenderModule -->
<g id="edge112" class="edge">
<title>MsgSenderService&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M5654,-150.96C5654,-129.34 5654,-96 5654,-96 5654,-96 8332.05,-96 8332.05,-96"/>
<polygon fill="black" stroke="black" points="8332.05,-99.5 8342.05,-96 8332.05,-92.5 8332.05,-99.5"/>
</g>
<!-- NotificationClient -->
<g id="node81" class="node">
<title>NotificationClient</title>
<ellipse fill="#fdb462" stroke="black" cx="5471" cy="-169" rx="81.11" ry="18"/>
<text text-anchor="middle" x="5471" y="-164.8" font-family="Times,serif" font-size="14.00">NotificationClient</text>
</g>
<!-- NotificationClient&#45;&gt;MgsSenderModule -->
<g id="edge113" class="edge">
<title>NotificationClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M5471,-150.71C5471,-128.79 5471,-95 5471,-95 5471,-95 8332.06,-95 8332.06,-95"/>
<polygon fill="black" stroke="black" points="8332.06,-98.5 8342.06,-95 8332.06,-91.5 8332.06,-98.5"/>
</g>
<!-- NotifierClient -->
<g id="node82" class="node">
<title>NotifierClient</title>
<ellipse fill="#fdb462" stroke="black" cx="5306" cy="-169" rx="65.42" ry="18"/>
<text text-anchor="middle" x="5306" y="-164.8" font-family="Times,serif" font-size="14.00">NotifierClient</text>
</g>
<!-- NotifierClient&#45;&gt;MgsSenderModule -->
<g id="edge114" class="edge">
<title>NotifierClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M5306,-150.99C5306,-128.51 5306,-93 5306,-93 5306,-93 8332.12,-93 8332.12,-93"/>
<polygon fill="black" stroke="black" points="8332.12,-96.5 8342.12,-93 8332.12,-89.5 8332.12,-96.5"/>
</g>
<!-- OrgchartClient -->
<g id="node83" class="node">
<title>OrgchartClient</title>
<ellipse fill="#fdb462" stroke="black" cx="5154" cy="-169" rx="68.92" ry="18"/>
<text text-anchor="middle" x="5154" y="-164.8" font-family="Times,serif" font-size="14.00">OrgchartClient</text>
</g>
<!-- OrgchartClient&#45;&gt;MgsSenderModule -->
<g id="edge115" class="edge">
<title>OrgchartClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M5154,-150.75C5154,-127.98 5154,-92 5154,-92 5154,-92 8332.34,-92 8332.34,-92"/>
<polygon fill="black" stroke="black" points="8332.34,-95.5 8342.34,-92 8332.34,-88.5 8332.34,-95.5"/>
</g>
<!-- PropertyClient -->
<g id="node84" class="node">
<title>PropertyClient</title>
<ellipse fill="#fdb462" stroke="black" cx="4999" cy="-169" rx="67.79" ry="18"/>
<text text-anchor="middle" x="4999" y="-164.8" font-family="Times,serif" font-size="14.00">PropertyClient</text>
</g>
<!-- PropertyClient&#45;&gt;MgsSenderModule -->
<g id="edge116" class="edge">
<title>PropertyClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M4999,-150.91C4999,-127.87 4999,-91 4999,-91 4999,-91 8331.99,-91 8331.99,-91"/>
<polygon fill="black" stroke="black" points="8331.99,-94.5 8341.99,-91 8331.99,-87.5 8331.99,-94.5"/>
</g>
<!-- StsClient -->
<g id="node85" class="node">
<title>StsClient</title>
<ellipse fill="#fdb462" stroke="black" cx="6535" cy="-169" rx="46.4" ry="18"/>
<text text-anchor="middle" x="6535" y="-164.8" font-family="Times,serif" font-size="14.00">StsClient</text>
</g>
<!-- StsClient&#45;&gt;MgsSenderModule -->
<g id="edge117" class="edge">
<title>StsClient&#45;&gt;MgsSenderModule</title>
<path fill="none" stroke="black" d="M6535,-150.89C6535,-131.66 6535,-104 6535,-104 6535,-104 8332.08,-104 8332.08,-104"/>
<polygon fill="black" stroke="black" points="8332.08,-107.5 8342.08,-104 8332.08,-100.5 8332.08,-107.5"/>
</g>
<!-- MsxEventStreamRepository -->
<g id="node86" class="node">
<title>MsxEventStreamRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="8182" cy="-169" rx="120.42" ry="18"/>
<text text-anchor="middle" x="8182" y="-164.8" font-family="Times,serif" font-size="14.00">MsxEventStreamRepository</text>
</g>
<!-- MsxEventStreamRepository&#45;&gt;MsxDomainModule -->
<g id="edge123" class="edge">
<title>MsxEventStreamRepository&#45;&gt;MsxDomainModule</title>
<path fill="none" stroke="black" d="M8273.29,-180.91C8273.29,-190.37 8273.29,-202 8273.29,-202 8273.29,-202 9444.25,-202 9444.25,-202 9444.25,-202 9444.25,-216.51 9444.25,-216.51"/>
<polygon fill="black" stroke="black" points="9440.75,-216.51 9444.25,-226.51 9447.75,-216.51 9440.75,-216.51"/>
</g>
<!-- MsxService -->
<g id="node87" class="node">
<title>MsxService</title>
<ellipse fill="#fdb462" stroke="black" cx="7986" cy="-169" rx="57.32" ry="18"/>
<text text-anchor="middle" x="7986" y="-164.8" font-family="Times,serif" font-size="14.00">MsxService</text>
</g>
<!-- MsxService&#45;&gt;MsxDomainModule -->
<g id="edge124" class="edge">
<title>MsxService&#45;&gt;MsxDomainModule</title>
<path fill="none" stroke="black" d="M8027.46,-181.66C8027.46,-191.33 8027.46,-203 8027.46,-203 8027.46,-203 9425.75,-203 9425.75,-203 9425.75,-203 9425.75,-216.94 9425.75,-216.94"/>
<polygon fill="black" stroke="black" points="9422.25,-216.94 9425.75,-226.94 9429.25,-216.94 9422.25,-216.94"/>
</g>
<!-- MsxService&#45;&gt;MsxQuerySideModule -->
<g id="edge132" class="edge">
<title>MsxService&#45;&gt;MsxQuerySideModule</title>
<path fill="none" stroke="black" d="M8011.76,-185.2C8011.76,-201.43 8011.76,-224 8011.76,-224 8011.76,-224 8244.43,-224 8244.43,-224 8244.43,-224 8244.43,-224.29 8244.43,-224.29"/>
<polygon fill="black" stroke="black" points="8240.93,-216.92 8244.43,-226.92 8247.93,-216.92 8240.93,-216.92"/>
</g>
<!-- MsxQueryRepository -->
<g id="node90" class="node">
<title>MsxQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="9769" cy="-321" rx="94.93" ry="18"/>
<text text-anchor="middle" x="9769" y="-316.8" font-family="Times,serif" font-size="14.00">MsxQueryRepository</text>
</g>
<!-- MsxQueryRepository&#45;&gt;MsxQuerySideModule -->
<g id="edge131" class="edge">
<title>MsxQueryRepository&#45;&gt;MsxQuerySideModule</title>
<path fill="none" stroke="black" d="M9769,-302.89C9769,-293.1 9769,-283 9769,-283 9769,-283 8178.46,-283 8178.46,-283 8178.46,-283 8178.46,-273.11 8178.46,-273.11"/>
<polygon fill="black" stroke="black" points="8181.96,-273.11 8178.46,-263.11 8174.96,-273.11 8181.96,-273.11"/>
</g>
<!-- PatchDataService  -->
<g id="node92" class="node">
<title>PatchDataService </title>
<polygon fill="#fb8072" stroke="black" points="13338.51,-483 13219.49,-483 13219.49,-447 13338.51,-447 13338.51,-483"/>
<text text-anchor="middle" x="13279" y="-460.8" font-family="Times,serif" font-size="14.00">PatchDataService </text>
</g>
<!-- PatchDataModule&#45;&gt;PatchDataService  -->
<g id="edge136" class="edge">
<title>PatchDataModule&#45;&gt;PatchDataService </title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M13274.02,-415.22C13274.02,-415.22 13274.02,-436.73 13274.02,-436.73"/>
<polygon fill="black" stroke="black" points="13270.52,-436.73 13274.02,-446.73 13277.52,-436.73 13270.52,-436.73"/>
</g>
<!-- PatchDataService -->
<g id="node93" class="node">
<title>PatchDataService</title>
<ellipse fill="#fdb462" stroke="black" cx="13299" cy="-321" rx="79.85" ry="18"/>
<text text-anchor="middle" x="13299" y="-316.8" font-family="Times,serif" font-size="14.00">PatchDataService</text>
</g>
<!-- PatchDataService&#45;&gt;PatchDataModule -->
<g id="edge137" class="edge">
<title>PatchDataService&#45;&gt;PatchDataModule</title>
<path fill="none" stroke="black" d="M13273.93,-338.25C13273.93,-338.25 13273.93,-368.76 13273.93,-368.76"/>
<polygon fill="black" stroke="black" points="13270.43,-368.76 13273.93,-378.76 13277.43,-368.76 13270.43,-368.76"/>
</g>
<!-- PermissionEventStreamRepository -->
<g id="node94" class="node">
<title>PermissionEventStreamRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="3817" cy="-169" rx="146.42" ry="18"/>
<text text-anchor="middle" x="3817" y="-164.8" font-family="Times,serif" font-size="14.00">PermissionEventStreamRepository</text>
</g>
<!-- PermissionEventStreamRepository&#45;&gt;PermissionDomainModule -->
<g id="edge144" class="edge">
<title>PermissionEventStreamRepository&#45;&gt;PermissionDomainModule</title>
<path fill="none" stroke="black" d="M3817,-187.27C3817,-227.06 3817,-318 3817,-318 3817,-318 8331.58,-318 8331.58,-318"/>
<polygon fill="black" stroke="black" points="8331.58,-321.5 8341.58,-318 8331.58,-314.5 8331.58,-321.5"/>
</g>
<!-- PermissionService -->
<g id="node95" class="node">
<title>PermissionService</title>
<ellipse fill="#fdb462" stroke="black" cx="4064" cy="-169" rx="82.82" ry="18"/>
<text text-anchor="middle" x="4064" y="-164.8" font-family="Times,serif" font-size="14.00">PermissionService</text>
</g>
<!-- PermissionService&#45;&gt;PermissionDomainModule -->
<g id="edge145" class="edge">
<title>PermissionService&#45;&gt;PermissionDomainModule</title>
<path fill="none" stroke="black" d="M4091.64,-186.13C4091.64,-223.9 4091.64,-311 4091.64,-311 4091.64,-311 8331.35,-311 8331.35,-311"/>
<polygon fill="black" stroke="black" points="8331.35,-314.5 8341.35,-311 8331.35,-307.5 8331.35,-314.5"/>
</g>
<!-- PermissionService&#45;&gt;PermissionQuerySideModule -->
<g id="edge153" class="edge">
<title>PermissionService&#45;&gt;PermissionQuerySideModule</title>
<path fill="none" stroke="black" d="M4036.36,-186.22C4036.36,-196.67 4036.36,-208 4036.36,-208 4036.36,-208 9261,-208 9261,-208 9261,-208 9261,-216.93 9261,-216.93"/>
<polygon fill="black" stroke="black" points="9257.5,-216.93 9261,-226.93 9264.5,-216.93 9257.5,-216.93"/>
</g>
<!-- PermissionQueryRepository -->
<g id="node98" class="node">
<title>PermissionQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="11094" cy="-321" rx="120.42" ry="18"/>
<text text-anchor="middle" x="11094" y="-316.8" font-family="Times,serif" font-size="14.00">PermissionQueryRepository</text>
</g>
<!-- PermissionQueryRepository&#45;&gt;PermissionQuerySideModule -->
<g id="edge152" class="edge">
<title>PermissionQueryRepository&#45;&gt;PermissionQuerySideModule</title>
<path fill="none" stroke="black" d="M11050.79,-304.14C11050.79,-289.96 11050.79,-272 11050.79,-272 11050.79,-272 9309.46,-272 9309.46,-272 9309.46,-272 9309.46,-271.12 9309.46,-271.12"/>
<polygon fill="black" stroke="black" points="9312.96,-273.16 9309.46,-263.16 9305.96,-273.16 9312.96,-273.16"/>
</g>
<!-- RedisCacheService -->
<g id="node100" class="node">
<title>RedisCacheService</title>
<ellipse fill="#fdb462" stroke="black" cx="11746" cy="-321" rx="86.23" ry="18"/>
<text text-anchor="middle" x="11746" y="-316.8" font-family="Times,serif" font-size="14.00">RedisCacheService</text>
</g>
<!-- RedisCacheService&#45;&gt;RedisCacheModule -->
<g id="edge155" class="edge">
<title>RedisCacheService&#45;&gt;RedisCacheModule</title>
<path fill="none" stroke="black" d="M11746,-302.95C11746,-268.11 11746,-195 11746,-195 11746,-195 8744.4,-195 8744.4,-195 8744.4,-195 8744.4,-216.95 8744.4,-216.95"/>
<polygon fill="black" stroke="black" points="8740.9,-216.95 8744.4,-226.95 8747.9,-216.95 8740.9,-216.95"/>
</g>
<!-- RoleEventStreamRepository -->
<g id="node101" class="node">
<title>RoleEventStreamRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="4309" cy="-169" rx="121.57" ry="18"/>
<text text-anchor="middle" x="4309" y="-164.8" font-family="Times,serif" font-size="14.00">RoleEventStreamRepository</text>
</g>
<!-- RoleEventStreamRepository&#45;&gt;RoleDomainModule -->
<g id="edge162" class="edge">
<title>RoleEventStreamRepository&#45;&gt;RoleDomainModule</title>
<path fill="none" stroke="black" d="M4309,-187.12C4309,-201.78 4309,-220 4309,-220 4309,-220 8549.73,-220 8549.73,-220 8549.73,-220 8549.73,-292.94 8549.73,-292.94"/>
<polygon fill="black" stroke="black" points="8546.23,-292.94 8549.73,-302.94 8553.23,-292.94 8546.23,-292.94"/>
</g>
<!-- RoleService -->
<g id="node102" class="node">
<title>RoleService</title>
<ellipse fill="#fdb462" stroke="black" cx="4507" cy="-169" rx="58.46" ry="18"/>
<text text-anchor="middle" x="4507" y="-164.8" font-family="Times,serif" font-size="14.00">RoleService</text>
</g>
<!-- RoleService&#45;&gt;RoleDomainModule -->
<g id="edge163" class="edge">
<title>RoleService&#45;&gt;RoleDomainModule</title>
<path fill="none" stroke="black" d="M4487.59,-186.14C4487.59,-200.27 4487.59,-218 4487.59,-218 4487.59,-218 8557.49,-218 8557.49,-218 8557.49,-218 8557.49,-292.59 8557.49,-292.59"/>
<polygon fill="black" stroke="black" points="8553.99,-292.59 8557.49,-302.59 8560.99,-292.59 8553.99,-292.59"/>
</g>
<!-- RoleService&#45;&gt;RoleQuerySideModule -->
<g id="edge171" class="edge">
<title>RoleService&#45;&gt;RoleQuerySideModule</title>
<path fill="none" stroke="black" d="M4526.41,-186.13C4526.41,-199.21 4526.41,-215 4526.41,-215 4526.41,-215 8844.41,-215 8844.41,-215 8844.41,-215 8844.41,-216.86 8844.41,-216.86"/>
<polygon fill="black" stroke="black" points="8840.91,-216.86 8844.41,-226.86 8847.91,-216.86 8840.91,-216.86"/>
</g>
<!-- RoleQueryRepository -->
<g id="node106" class="node">
<title>RoleQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="12120" cy="-321" rx="95.57" ry="18"/>
<text text-anchor="middle" x="12120" y="-316.8" font-family="Times,serif" font-size="14.00">RoleQueryRepository</text>
</g>
<!-- RoleQueryRepository&#45;&gt;RoleQuerySideModule -->
<g id="edge170" class="edge">
<title>RoleQueryRepository&#45;&gt;RoleQuerySideModule</title>
<path fill="none" stroke="black" d="M12120,-302.81C12120,-287.82 12120,-269 12120,-269 12120,-269 8891.46,-269 8891.46,-269 8891.46,-269 8891.46,-268.42 8891.46,-268.42"/>
<polygon fill="black" stroke="black" points="8894.96,-273.19 8891.46,-263.19 8887.96,-273.19 8894.96,-273.19"/>
</g>
<!-- UserDomainService -->
<g id="node109" class="node">
<title>UserDomainService</title>
<ellipse fill="#fdb462" stroke="black" cx="10025" cy="-541" rx="89.16" ry="18"/>
<text text-anchor="middle" x="10025" y="-536.8" font-family="Times,serif" font-size="14.00">UserDomainService</text>
</g>
<!-- UserDomainService&#45;&gt;UserDomainModule -->
<g id="edge185" class="edge">
<title>UserDomainService&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M9967.94,-527.12C9967.94,-506.2 9967.94,-469 9967.94,-469 9967.94,-469 9076.7,-469 9076.7,-469"/>
<polygon fill="black" stroke="black" points="9076.7,-465.5 9066.7,-469 9076.7,-472.5 9076.7,-465.5"/>
</g>
<!-- UserEventStreamRepository -->
<g id="node110" class="node">
<title>UserEventStreamRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="9796" cy="-541" rx="121.55" ry="18"/>
<text text-anchor="middle" x="9796" y="-536.8" font-family="Times,serif" font-size="14.00">UserEventStreamRepository</text>
</g>
<!-- UserEventStreamRepository&#45;&gt;UserDomainModule -->
<g id="edge186" class="edge">
<title>UserEventStreamRepository&#45;&gt;UserDomainModule</title>
<path fill="none" stroke="black" d="M9770.13,-523.24C9770.13,-503.99 9770.13,-476 9770.13,-476 9770.13,-476 9076.58,-476 9076.58,-476"/>
<polygon fill="black" stroke="black" points="9076.58,-472.5 9066.58,-476 9076.58,-479.5 9076.58,-472.5"/>
</g>
<!-- UserExportRepository -->
<g id="node113" class="node">
<title>UserExportRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="862" cy="-245" rx="97.85" ry="18"/>
<text text-anchor="middle" x="862" y="-240.8" font-family="Times,serif" font-size="14.00">UserExportRepository</text>
</g>
<!-- UserExportRepository&#45;&gt;UserExportModule -->
<g id="edge196" class="edge">
<title>UserExportRepository&#45;&gt;UserExportModule</title>
<path fill="none" stroke="black" d="M862,-263.01C862,-279.05 862,-300 862,-300 862,-300 1231.59,-300 1231.59,-300 1231.59,-300 1231.59,-300.29 1231.59,-300.29"/>
<polygon fill="black" stroke="black" points="1228.09,-292.92 1231.59,-302.92 1235.09,-292.92 1228.09,-292.92"/>
</g>
<!-- UserExportService -->
<g id="node114" class="node">
<title>UserExportService</title>
<ellipse fill="#fdb462" stroke="black" cx="1062" cy="-245" rx="84.51" ry="18"/>
<text text-anchor="middle" x="1062" y="-240.8" font-family="Times,serif" font-size="14.00">UserExportService</text>
</g>
<!-- UserExportService&#45;&gt;UserExportModule -->
<g id="edge197" class="edge">
<title>UserExportService&#45;&gt;UserExportModule</title>
<path fill="none" stroke="black" d="M1146.57,-245C1158.99,-245 1167.71,-245 1167.71,-245 1167.71,-245 1167.71,-321 1167.71,-321 1167.71,-321 1171.7,-321 1171.7,-321"/>
<polygon fill="black" stroke="black" points="1171.7,-324.5 1181.7,-321 1171.7,-317.5 1171.7,-324.5"/>
</g>
<!-- UserQueryRepository -->
<g id="node118" class="node">
<title>UserQueryRepository</title>
<ellipse fill="#fdb462" stroke="black" cx="9770" cy="-397" rx="95.55" ry="18"/>
<text text-anchor="middle" x="9770" y="-392.8" font-family="Times,serif" font-size="14.00">UserQueryRepository</text>
</g>
<!-- UserQueryRepository&#45;&gt;UserQuerySideModule -->
<g id="edge207" class="edge">
<title>UserQueryRepository&#45;&gt;UserQuerySideModule</title>
<path fill="none" stroke="black" d="M9703.5,-384C9558.69,-384 9227.69,-384 9227.69,-384 9227.69,-384 9227.69,-349.22 9227.69,-349.22"/>
<polygon fill="black" stroke="black" points="9231.19,-349.22 9227.69,-339.22 9224.19,-349.22 9231.19,-349.22"/>
</g>
<!-- UserService -->
<g id="node119" class="node">
<title>UserService</title>
<ellipse fill="#fdb462" stroke="black" cx="9942" cy="-397" rx="58.44" ry="18"/>
<text text-anchor="middle" x="9942" y="-392.8" font-family="Times,serif" font-size="14.00">UserService</text>
</g>
<!-- UserService&#45;&gt;UserQuerySideModule -->
<g id="edge208" class="edge">
<title>UserService&#45;&gt;UserQuerySideModule</title>
<path fill="none" stroke="black" d="M9943.91,-379C9943.91,-370 9943.91,-361 9943.91,-361 9943.91,-361 9173,-361 9173,-361 9173,-361 9173,-349.13 9173,-349.13"/>
<polygon fill="black" stroke="black" points="9176.5,-349.13 9173,-339.13 9169.5,-349.13 9176.5,-349.13"/>
</g>
</g>
</svg>

    </div>
    <i id="fullscreen" class="icon ion-ios-resize module-graph-fullscreen-btn" aria-hidden="true"></i>
    <div class="btn-group">
        <button id="zoom-in" class="btn btn-default btn-sm">Zoom in</button>
        <button id="reset" class="btn btn-default btn-sm">Reset</button>
        <button id="zoom-out" class="btn btn-default btn-sm">Zoom out</button>
    </div>
</div>
  
<div class="container-fluid overview">
    <div class="row">
        <div class="col-sm-3">
            <div class="card text-center">
                <div class="card-block">
                    <h4 class="card-title"><span class="icon ion-ios-archive"></span></h4>
                    <p class="card-text">
                        <a href="./modules.html">25 Modules</a>
                    </p>
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="card text-center">
                <div class="card-block">
                    <h4 class="card-title"><span class="icon ion-md-swap"></span></h4>
                    <p class="card-text">21 Controllers</p>
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="card text-center">
                <div class="card-block">
                    <h4 class="card-title"><span class="icon ion-md-arrow-round-down"></span></h4>
                    <p class="card-text">66 Injectables</p>
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="card text-center">
                <div class="card-block">
                    <h4 class="card-title"><span class="icon ion-ios-paper"></span></h4>
                    <p class="card-text">193 Classes</p>
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="card text-center">
                <div class="card-block">
                    <h4 class="card-title"><span class="icon ion-ios-lock"></span></h4>
                    <p class="card-text">2 Guards</p>
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="card text-center">
                <div class="card-block">
                    <h4 class="card-title"><span class="icon ion-md-information-circle-outline"></span></h4>
                    <p class="card-text">38 Interfaces</p>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="js/libs/svg-pan-zoom.min.js"></script>
<script src="js/svg-pan-zoom.controls.js"></script> 











                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 0;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'overview';
            var COMPODOC_CURRENT_PAGE_URL = 'overview.html';
       </script>

       <script src="./js/libs/custom-elements.min.js"></script>
       <script src="./js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="./js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="./js/menu-wc.js" defer></script>

       <script src="./js/libs/bootstrap-native.js"></script>

       <script src="./js/libs/es6-shim.min.js"></script>
       <script src="./js/libs/EventDispatcher.js"></script>
       <script src="./js/libs/promise.min.js"></script>
       <script src="./js/libs/zepto.min.js"></script>

       <script src="./js/compodoc.js"></script>

       <script src="./js/tabs.js"></script>
       <script src="./js/menu.js"></script>
       <script src="./js/libs/clipboard.min.js"></script>
       <script src="./js/libs/prism.js"></script>
       <script src="./js/sourceCode.js"></script>
          <script src="./js/search/search.js"></script>
          <script src="./js/search/lunr.min.js"></script>
          <script src="./js/search/search-lunr.js"></script>
          <script src="./js/search/search_index.js"></script>
       <script src="./js/lazy-load-graphs.js"></script>


    </body>
</html>
