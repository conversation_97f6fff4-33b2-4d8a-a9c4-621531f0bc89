<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-sts documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-sts documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content controller">
                   <div class="content-data">





<ol class="breadcrumb">
  <li>Controllers</li>
  <li>ListenerController</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/listener/listener.controller.ts</code>
        </p>






            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#changeActiveStatus">changeActiveStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkAndUpdateAdmin">checkAndUpdateAdmin</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkAndUpdateUser">checkAndUpdateUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkUserExisted">checkUserExisted</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkUserExistedCare">checkUserExistedCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countUser">countUser</a>
                            </li>
                            <li>
                                <a href="#createAllUserForEmployee">createAllUserForEmployee</a>
                            </li>
                            <li>
                                <a href="#createUserForCareCustomer">createUserForCareCustomer</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createUserForCareCustomerAuto">createUserForCareCustomerAuto</a>
                            </li>
                            <li>
                                <a href="#createUserForEmployee">createUserForEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findByQuery">findByQuery</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findManyUser">findManyUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#generateAccessTokenByUser">generateAccessTokenByUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getAllRole">getAllRole</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getAvaiableAccountants">getAvaiableAccountants</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getEmployeeIdByRole">getEmployeeIdByRole</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getEmployeeIdsByRole">getEmployeeIdsByRole</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getListRolesByIds">getListRolesByIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getListUserByEmails">getListUserByEmails</a>
                            </li>
                            <li>
                                <a href="#getLoginByEmployee">getLoginByEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getMasterData">getMasterData</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getRoleById">getRoleById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getRoleByRoleName">getRoleByRoleName</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTokenByUserId">getTokenByUserId</a>
                            </li>
                            <li>
                                <a href="#getUserByCodeDX">getUserByCodeDX</a>
                            </li>
                            <li>
                                <a href="#getUserById">getUserById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getUserByQuery">getUserByQuery</a>
                            </li>
                            <li>
                                <a href="#getUserByRole">getUserByRole</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getUserListByRoleId">getUserListByRoleId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getUserListByUserIds">getUserListByUserIds</a>
                            </li>
                            <li>
                                <a href="#listenerOrgchart">listenerOrgchart</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#registerEvent">registerEvent</a>
                            </li>
                            <li>
                                <a href="#resetUserRole">resetUserRole</a>
                            </li>
                            <li>
                                <a href="#setupFeature">setupFeature</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#signedUser">signedUser</a>
                            </li>
                            <li>
                                <a href="#updateUserActiveOrInactive">updateUserActiveOrInactive</a>
                            </li>
                            <li>
                                <a href="#updateUserCare">updateUserCare</a>
                            </li>
                            <li>
                                <a href="#updateUserPermission">updateUserPermission</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#verifyOtp">verifyOtp</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="changeActiveStatus"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            changeActiveStatus
                        </b>
                        <a href="#changeActiveStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>changeActiveStatus(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="445"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:445</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;changeActiveStatus&#x27;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkAndUpdateAdmin"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkAndUpdateAdmin
                        </b>
                        <a href="#checkAndUpdateAdmin"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkAndUpdateAdmin(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="406"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:406</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkAndUpdateUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkAndUpdateUser
                        </b>
                        <a href="#checkAndUpdateUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkAndUpdateUser(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="401"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:401</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkUserExisted"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkUserExisted
                        </b>
                        <a href="#checkUserExisted"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkUserExisted(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="204"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:204</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkUserExistedCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkUserExistedCare
                        </b>
                        <a href="#checkUserExistedCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkUserExistedCare(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="224"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:224</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countUser
                        </b>
                        <a href="#countUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countUser(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="412"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:412</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;countUser&#x27;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createAllUserForEmployee"></a>
                    <span class="name">
                        <b>
                            createAllUserForEmployee
                        </b>
                        <a href="#createAllUserForEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createAllUserForEmployee(dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="135"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:135</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createUserForCareCustomer"></a>
                    <span class="name">
                        <b>
                            createUserForCareCustomer
                        </b>
                        <a href="#createUserForCareCustomer"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createUserForCareCustomer(dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="152"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:152</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createUserForCareCustomerAuto"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createUserForCareCustomerAuto
                        </b>
                        <a href="#createUserForCareCustomerAuto"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createUserForCareCustomerAuto(dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="159"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:159</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createUserForEmployee"></a>
                    <span class="name">
                        <b>
                            createUserForEmployee
                        </b>
                        <a href="#createUserForEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createUserForEmployee(dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="145"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:145</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findByQuery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findByQuery
                        </b>
                        <a href="#findByQuery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findByQuery(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="431"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:431</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;findQueryUser&#x27;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findManyUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findManyUser
                        </b>
                        <a href="#findManyUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findManyUser(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="418"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:418</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;findManyUser&#x27;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateAccessTokenByUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            generateAccessTokenByUser
                        </b>
                        <a href="#generateAccessTokenByUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>generateAccessTokenByUser(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="438"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:438</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;generateAccessTokenByUser&#x27;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllRole"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getAllRole
                        </b>
                        <a href="#getAllRole"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllRole()</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="362"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:362</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAvaiableAccountants"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getAvaiableAccountants
                        </b>
                        <a href="#getAvaiableAccountants"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAvaiableAccountants(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="317"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:317</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;string[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getEmployeeIdByRole"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getEmployeeIdByRole
                        </b>
                        <a href="#getEmployeeIdByRole"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getEmployeeIdByRole(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="255"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:255</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;string[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getEmployeeIdsByRole"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getEmployeeIdsByRole
                        </b>
                        <a href="#getEmployeeIdsByRole"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getEmployeeIdsByRole(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="275"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:275</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;string[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getListRolesByIds"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getListRolesByIds
                        </b>
                        <a href="#getListRolesByIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getListRolesByIds(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="352"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:352</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getListUserByEmails"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getListUserByEmails
                        </b>
                        <a href="#getListUserByEmails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getListUserByEmails(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="340"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:340</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLoginByEmployee"></a>
                    <span class="name">
                        <b>
                            getLoginByEmployee
                        </b>
                        <a href="#getLoginByEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getLoginByEmployee(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="334"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:334</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getMasterData"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getMasterData
                        </b>
                        <a href="#getMasterData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getMasterData(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="425"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:425</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;getMasterData&#x27;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getRoleById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getRoleById
                        </b>
                        <a href="#getRoleById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getRoleById(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="370"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:370</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getRoleByRoleName"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getRoleByRoleName
                        </b>
                        <a href="#getRoleByRoleName"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getRoleByRoleName(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="268"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:268</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTokenByUserId"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getTokenByUserId
                        </b>
                        <a href="#getTokenByUserId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTokenByUserId(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="390"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:390</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserByCodeDX"></a>
                    <span class="name">
                        <b>
                            getUserByCodeDX
                        </b>
                        <a href="#getUserByCodeDX"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getUserByCodeDX(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="165"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:165</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserById"></a>
                    <span class="name">
                        <b>
                            getUserById
                        </b>
                        <a href="#getUserById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getUserById(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="187"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:187</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserByQuery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getUserByQuery
                        </b>
                        <a href="#getUserByQuery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserByQuery(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="235"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:235</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserByRole"></a>
                    <span class="name">
                        <b>
                            getUserByRole
                        </b>
                        <a href="#getUserByRole"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getUserByRole(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="396"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:396</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserListByRoleId"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getUserListByRoleId
                        </b>
                        <a href="#getUserListByRoleId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserListByRoleId(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="307"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:307</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserListByUserIds"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getUserListByUserIds
                        </b>
                        <a href="#getUserListByUserIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserListByUserIds(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="297"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:297</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listenerOrgchart"></a>
                    <span class="name">
                        <b>
                            listenerOrgchart
                        </b>
                        <a href="#listenerOrgchart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>listenerOrgchart(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="116"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:116</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="registerEvent"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            registerEvent
                        </b>
                        <a href="#registerEvent"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>registerEvent(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, notiSystem: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="380"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:380</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>notiSystem</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="resetUserRole"></a>
                    <span class="name">
                        <b>
                            resetUserRole
                        </b>
                        <a href="#resetUserRole"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>resetUserRole(role)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="192"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:192</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>role</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="setupFeature"></a>
                    <span class="name">
                        <b>
                            setupFeature
                        </b>
                        <a href="#setupFeature"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>setupFeature(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="198"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:198</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="signedUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            signedUser
                        </b>
                        <a href="#signedUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>signedUser(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="40"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:40</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateUserActiveOrInactive"></a>
                    <span class="name">
                        <b>
                            updateUserActiveOrInactive
                        </b>
                        <a href="#updateUserActiveOrInactive"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>updateUserActiveOrInactive(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="177"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:177</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateUserCare"></a>
                    <span class="name">
                        <b>
                            updateUserCare
                        </b>
                        <a href="#updateUserCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>updateUserCare(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="182"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:182</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateUserPermission"></a>
                    <span class="name">
                        <b>
                            updateUserPermission
                        </b>
                        <a href="#updateUserPermission"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>updateUserPermission(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="170"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:170</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="verifyOtp"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            verifyOtp
                        </b>
                        <a href="#verifyOtp"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>verifyOtp(pattern: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Decorators : </b>
                    <br />
                    <code>@MessagePattern({cmd: undefined})<br /></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="385"
                            class="link-to-prism">src/modules/listener/listener.controller.ts:385</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>pattern</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Controller, Body, Param, Headers} from &#x27;@nestjs/common&#x27;;
import { MessagePattern } from &#x27;@nestjs/microservices&#x27;;
import { UrlConst } from &#x27;../shared/constant/url.const&#x27;;
import { CmdPatternConst } from &#x27;../shared/constant/cmd-pattern.const&#x27;;
import { MsxLoggerService } from &#x27;../logger/logger.service&#x27;;
import { AuthService } from &#x27;../auth.domain/service&#x27;;
import { IUserResponse } from &#x27;../shared/services/user/interfaces/user-by-id.interface&#x27;;
import { UserService } from &#x27;../user.queryside/service&#x27;;
import { RoleService } from &#x27;./../role.queryside/service&#x27;;
import { CommonConst } from &#x27;../shared/constant/common.const&#x27;;
import { UserDomainService } from &#x27;../user.domain/service&#x27;;
import { FeatureService } from &#x27;../feature.queryside/service&#x27;;
import { CommonUtils } from &#x27;../shared/classes/class-utils&#x27;;
import { UserQueryRepository } from &#x27;../user.queryside/repository/query.repository&#x27;;
import { RoleEnum } from &#x27;../shared/services/user/enum/role.enum&#x27;;
import { ConfigService } from &#x27;../config/config.service&#x27;;
import { RedisCacheService } from &#x27;../redis-cache/service&#x27;
import { CacheKeyConst } from &#x27;../shared/constant/cache.const&#x27;
import * as _ from &#x27;lodash&#x27;;
import {MasterQueryService} from &quot;../master/service&quot;;

@Controller(UrlConst.USER_PATH)
export class ListenerController {
    private readonly context &#x3D; ListenerController.name;
    constructor(
        private readonly loggerService: MsxLoggerService,
        private readonly authService: AuthService,
        private readonly userService: UserService,
        private readonly roleService: RoleService,
        private readonly userDomainService: UserDomainService,
        private readonly featureService: FeatureService,
        private readonly repository: UserQueryRepository,
        private readonly redisCacheService: RedisCacheService,
        private readonly configService: ConfigService,
        private readonly masterService: MasterQueryService,
    ) { }

    // Use for all service to verify token
    @MessagePattern({ cmd: CmdPatternConst.USER.SIGNED })
    async signedUser(pattern: any) {

        this.loggerService.log(this.context, &#x27;Verify user &#x3D;&gt; &#x27;, pattern);
        const data &#x3D; pattern.data;
        if (data.notiSystem &amp;&amp; this.configService.get(&#x27;BLACK_LIST_CLIENT&#x27;)) {
          const blackList &#x3D; this.configService.get(&#x27;BLACK_LIST_CLIENT&#x27;).split(&#x27;;&#x27;);
          if (blackList.includes(data.notiSystem)) {
            return null;
          }
        }
        const getFromCache &#x3D; this.configService.get(&#x27;CACHE_USER_PERMISSION&#x27;) &#x3D;&#x3D;&#x3D; &#x27;true&#x27;;
        let cachePermission &#x3D; null;
        if (getFromCache) {
            cachePermission &#x3D; await this.redisCacheService.get(&#x60;${CacheKeyConst.USER_PERMISSION}${data.data}&#x60;);
            if (cachePermission) {
                if ((data.svc || []).every(e &#x3D;&gt; cachePermission.svc.includes(e))) {
                    return Promise.resolve({
                        ...cachePermission.user,
                        roles: cachePermission.roles
                    });
                }
            } else {
                cachePermission &#x3D; {
                    svc: [],
                    roles: [],
                    user: {}
                }
            }
        }
        return this.userService.findOneUserAndRole(data)
            .then(async (response) &#x3D;&gt; {
                // this.loggerService.log(this.context, &#x27;Verify user reponse &#x3D;&gt; &#x27;, response);
                this.loggerService.log(this.context, &#x27;Verify user reponse &#x3D;&gt; &#x27;);
                if (response &amp;&amp; response.id) {
                    const result: IUserResponse &#x3D; {
                        id: response.id,
                        name: response.name,
                        email: response.email,
                        roles: [],
                        status: response.status,
                        roleId: response.roleId,
                        registerStatus: response.registerStatus,
                        phone: response.phone,
                        codeDX: response.codeDX,
                        type: response.type,
                        otp: response.otp,
                        authOtp: response.authOtp,
                        pos: response.pos
                    };
                    if (response.permissions) {
                        response.permissions.forEach((permission) &#x3D;&gt; {
                            if (((data.svc.indexOf(permission.msxName) &gt; -1) || (permission.msxName &#x3D;&#x3D;&#x3D; &#x27;SYSTEM&#x27;)) &amp;&amp;
                                !result.roles.some((a) &#x3D;&gt; a &#x3D;&#x3D;&#x3D; permission.featureName)) {

                                return result.roles.push(permission.featureName);
                            }
                        });
                    }

                    if (getFromCache) {
                        cachePermission.svc &#x3D; _.uniq(cachePermission.svc.concat(data.svc));
                        cachePermission.roles &#x3D; _.uniq(cachePermission.roles.concat(result.roles));
                        cachePermission.user &#x3D; { ...result };
                        delete cachePermission.user.roles;

                        await this.redisCacheService.set(&#x60;${CacheKeyConst.USER_PERMISSION}${response.id}&#x60;, cachePermission);
                    }
                    return (result);
                }
            }).catch((error) &#x3D;&gt; {
                this.loggerService.error(this.context, &#x27;Has error &#x3D;&gt; &#x27;, error);
                return error;
            });
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.ORGCHART })
    listenerOrgchart(pattern: any) {
        const data &#x3D; pattern.data;
        const action &#x3D; data.action;
        const model &#x3D; data.model;
        switch (action) {
            case CommonConst.AGGREGATES_LISTENER.ORGCHART.UPDATED:
                this.loggerService.log(this.context, &#x27;RedisListenerController/UPDATED &#x27;);
                this.userService.update(model);
                break;
            case CommonConst.AGGREGATES_LISTENER.ORGCHART.LIST_UPDATED:
                this.loggerService.log(this.context, &#x27;RedisListenerController/LIST_UPDATED&#x27;);
                this.userService.updateList(model);
                break;
            default:
                this.loggerService.log(this.context, &#x27;Data invalid &#x3D;&gt; &#x27;, data);
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.CREATE_ALL_USER_WITH_PASSWORD })
    createAllUserForEmployee(@Body() dto: any) {
        this.loggerService.log(this.context, &#x27;Create user with password&#x27;);
        if (dto.data &amp;&amp; dto.data.length) {
            dto.data.forEach(element &#x3D;&gt; {
                this.userDomainService.createUserWithPassword(element, element.notiUser);
            });
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.CREATE_USER_WITH_PASSWORD })
    createUserForEmployee(@Body() dto: any) {
        this.loggerService.log(this.context, &#x27;Create user with password&#x27;);

        this.userDomainService.createUserWithPassword(dto.data, dto.data.notiUser);
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.CREATE_USER_WITH_PASSWORD_CARE })
    createUserForCareCustomer(@Body() dto: any) {
        this.loggerService.log(this.context, &#x27;Create user with password care&#x27;);
        
        this.userDomainService.createUserWithPasswordCare(dto.data);
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.CREATE_USER_CARE_AUTO })
    async createUserForCareCustomerAuto(@Body() dto: any) {
        this.loggerService.log(this.context, &#x27;Create user care auto&#x27;);
        return await this.userDomainService.createUserCareAuto(dto.data);
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.GET_USER_BY_CODEDX })
    getUserByCodeDX(pattern: any) {
        return this.repository.findOne({ codeDX: pattern.data })
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.UPDATE_USER_PERMISSION })
    updateUserPermission(@Body() pattern: any) {
        // console.log(&#x27;4&#x27;);
        // this.loggerService.log(this.context, &#x27;Create user for customer&#x27;, dto.data.email);
        this.userDomainService.setUserPermission(pattern.data, &#x27;&#x27;);
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.UPDATE_USER_ACTIVE })
    updateUserActiveOrInactive(@Body() pattern: any) {
        this.userDomainService.updateUserActiveOrInactive(pattern.data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.CARE.UPDATE_USER })
    updateUserCare(@Body() pattern: any) {
        this.userDomainService.updateUserCare(pattern.data);
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.GET_USER_BY_ID })
    getUserById(pattern: any) {
        return this.repository.findOne({ id: pattern.data })
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.RESET_ROLE })
    resetUserRole(role) {
        this.loggerService.log(this.context, &#x27;resetRole &#x3D;&gt; &#x27;, role);
        this.userService.resetUserRole(role);
    }

    @MessagePattern({ cmd: CmdPatternConst.FEATURE.SETTUP })
    setupFeature(pattern: any) {
        this.loggerService.log(this.context, &#x27;setupFeature &#x3D;&gt; &#x27;, pattern);
        this.featureService.tryInstallFeatures(pattern.data);
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.IS_EXISTED })
    async checkUserExisted(pattern: any) {
        this.loggerService.log(this.context, &#x27;checkUserExisted &#x3D;&gt; &#x27;, pattern);
        if (!pattern || !pattern.data || (!pattern.data.email &amp;&amp; !pattern.data.phone)) {
            return false;
        }

        // Fake phone number incase null query
        let phone &#x3D; pattern.data.phone ? pattern.data.phone : &quot;O2O&quot;;
        let email &#x3D; pattern.data.email ? pattern.data.email : &quot;DXS&quot;;

        const user &#x3D; await this.userService.getUserByEmailOrPhone(
            {
                email: email,
                phone: phone
            }
        );
        return CommonUtils.objectNotEmpty(user);
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.IS_EXISTED_CARE })
    async checkUserExistedCare(pattern: any) {
      this.loggerService.log(this.context, &#x27;checkUserExistedCare &#x3D;&gt; &#x27;, pattern);
      if (!pattern || !pattern.data || (!pattern.data.email &amp;&amp; !pattern.data.phone)) {
        return false;
      }

      const user &#x3D; await this.userService.getUserByEmailOrPhoneCare(pattern.data);
      return CommonUtils.objectNotEmpty(user);
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.GET_USER_BY_PHONE_EMAIL })
    async getUserByQuery(pattern: any) {
        this.loggerService.log(this.context, &#x27;getUserByQuery &#x3D;&gt; &#x27;, pattern);
        if (!pattern || !pattern.data || (!pattern.data.email &amp;&amp; !pattern.data.phone)) {
            return null;
        }

        // Fake phone number incase null query
        let phone &#x3D; pattern.data.phone ? pattern.data.phone : &quot;O2O&quot;;
        let email &#x3D; pattern.data.email ? pattern.data.email : &quot;DXS&quot;;

        const user &#x3D; await this.userService.getUserByEmailOrPhone(
            {
                email: email,
                phone: phone
            }
        );
        return user;
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.EMPLOYEE })
    async getEmployeeIdByRole(pattern: any): Promise&lt;string[]&gt; {
        let result &#x3D; [];
        if (pattern.data.action &#x3D;&#x3D;&#x3D; &#x27;employeeRole&#x27;) {
            const accountantRoles: string[] &#x3D; [&#x27;Kế toán trưởng&#x27;, &#x27;Kế toán&#x27;];
            for (const role of accountantRoles) {
                result &#x3D; result.concat(await this.userService.getUserByRole(role)
                    .then((res) &#x3D;&gt; res.map((user) &#x3D;&gt; user.id)));
            }
        }
        return result;
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.ROLE_BY_ROLE_NAME })
    async getRoleByRoleName(pattern: any): Promise&lt;any&gt; {
        if(pattern.data &amp;&amp; pattern.data.roleName) {
            return await this.userService.getRoleByRoleName(pattern.data.roleName);
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.ORGCHART })
    async getEmployeeIdsByRole(pattern: any): Promise&lt;string[]&gt; {
        switch (pattern.data.action){
            case CommonConst.AGGREGATES_LISTENER.ORGCHART.ACCOUNTANT: {
                return await this.repository.findUserByRoleId(RoleEnum.KE_TOAN).then((res) &#x3D;&gt; res.map((user) &#x3D;&gt; user.id)) || [];
            }
            case CommonConst.AGGREGATES_LISTENER.ORGCHART.DEVBUSINESS: {
                return await this.repository.findUserByRoleId(RoleEnum.PHAT_TRIEN_KINH_DOANH).then((res) &#x3D;&gt; res.map((user) &#x3D;&gt; user.id)) || [];
            }
            case CommonConst.AGGREGATES_LISTENER.ORGCHART.SALE_ADMIN: {
                return await this.repository.findUserByRoleId(RoleEnum.SALE_ADMIN).then((res) &#x3D;&gt; res.map((user) &#x3D;&gt; user.id)) || [];
            }
            case CommonConst.AGGREGATES_LISTENER.ORGCHART.PERMISSION: {
                return await this.repository.findUserByPermission(pattern.data.permission).then((res) &#x3D;&gt; res.map((user) &#x3D;&gt; user.id)) || [];
            }
            default: {
                this.loggerService.log(this.context, &#x27;Data unsupport &#x3D;&gt; &#x27; + pattern.data.action);
                return [];
            }
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.GET_USER_LIST_BY_USERIDS })
    async getUserListByUserIds(pattern: any) {
        try {
            const users &#x3D; await this.repository.getUserListByUserIds(pattern.data).then();
            return users;
        } catch (error) {
            return [];
        }
    }
    
    @MessagePattern({ cmd: CmdPatternConst.USER.GET_USER_LIST_BY_ROLE_ID })
    async getUserListByRoleId(pattern: any) {
        try {
            const users &#x3D; await this.repository.findUserByRoleId(pattern.data).then();
            return users;
        } catch (error) {
            return [];
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.TRANSACTION })
    async getAvaiableAccountants(pattern: any): Promise&lt;string[]&gt; {
        const data &#x3D; pattern.data;
        const action &#x3D; data.action;
        const model &#x3D; data.model;
        switch (action) { 
            case CommonConst.AGGREGATES_LISTENER.TRANSACTION.GET_ACCOUNTANTS: { 
                const accountants &#x3D; await this.repository.getAvaiableAccountants();
                return accountants || [];
            }
            default: {
                this.loggerService.log(this.context, &#x27;Data unsupport &#x3D;&gt; &#x27; + action);
                return null;
            }
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.AUTH.LOGIN_BY_EMPLOYEE })
    getLoginByEmployee(pattern: any) {
        const data &#x3D; pattern.data;
        return this.authService.findLoginByUser(data.employeeId, data.query);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.CONTRACT })
    async getListUserByEmails(pattern: any) {
        try {
            const data &#x3D; pattern.data;
            const emails &#x3D; data.emails;
            const query  &#x3D; { &quot;email&quot;: { &#x27;$in&#x27;: emails } }
            return await this.repository.find(query);
        } catch (error) {
            return [];
        }
    }
    
    @MessagePattern({ cmd: CmdPatternConst.USER.GET_ROLE })
    async getListRolesByIds(pattern: any) {
        try {
            const data &#x3D; pattern.data;
            return await this.repository.getListRolesByIds(data);
        } catch (error) {
            return [];
        }
    }
    
    @MessagePattern({ cmd: CmdPatternConst.USER.GET_ALL_ROLE })
    async getAllRole() {
        try {
            return await this.roleService.GetAll();
        } catch (error) {
            return [];
        }
    }
    @MessagePattern({ cmd: CmdPatternConst.USER.GET_ROLE_BY_ID })
    async getRoleById(pattern: any) {
        try {
            const data &#x3D; pattern.data;
            return await this.roleService.getRoleById(data);
        } catch (error) {
            return [];
        }
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.REGISTER_EVENT })
    async registerEvent(@Body() pattern: any, @Headers(&#x27;x-client-code&#x27;) notiSystem: string){
        return await this.userDomainService.registerEvent(pattern.data, notiSystem);
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.USER_VERIFY_OTP })
    async verifyOtp(@Body() pattern: any){
        let data &#x3D; pattern.data;
        return await this.userDomainService.verifyOtp(data.otp, data.token);
    }
    @MessagePattern({ cmd: CmdPatternConst.USER.USER_GET_TOKEN })
    async getTokenByUserId(@Body() pattern: any){
        let data &#x3D; pattern.data;
        return await this.userDomainService.getTokenByUserId(data.id, data.data);
    }
    
    @MessagePattern({cmd: CmdPatternConst.USER.ROLE_GET_USER})
    getUserByRole(@Body() pattern: any){
        const data: any &#x3D; pattern.data;
        return this.userService.findUserByRoleId(data.roleId);
    }
    @MessagePattern({cmd: CmdPatternConst.USER.CHECK_AND_UPDATE_USER})
    async checkAndUpdateUser(@Body() pattern: any){
        const data: any &#x3D; pattern.data;
        return await this.userDomainService.checkAndUpdateUser(data);
    }
    @MessagePattern({cmd: CmdPatternConst.USER.CHECK_AND_UPDATE_ADMIN})
    async checkAndUpdateAdmin(@Body() pattern: any){
        const data: any &#x3D; pattern.data;
        return await this.userDomainService.checkAndUpdateAdmin(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.COUNT })
    async countUser(@Body() pattern: any, actionName &#x3D; &#x27;countUser&#x27;) {
      this.loggerService.log(this.context, &#x60;[${actionName}]&#x60;);
      return await this.repository.countAll();
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.FIND_WITH_QUERY })
    async findManyUser(@Body() pattern: any, actionName &#x3D; &#x27;findManyUser&#x27;) {
      this.loggerService.log(this.context, &#x60;[${actionName}]&#x60;);
      const filter: any &#x3D; pattern.data;
      return await this.repository.findMany(filter);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_MASTER_DATA })
    async getMasterData(pattern: any, actionName &#x3D; &#x27;getMasterData&#x27;) {
      this.loggerService.log(this.context, &#x60;[${actionName}]&#x60;);
      return await this.masterService.findMasterData();
    }

    @MessagePattern({ cmd: CmdPatternConst.USER.FIND_BY_QUERY })
    async findByQuery(@Body() pattern: any, actionName &#x3D; &#x27;findQueryUser&#x27;) {
      this.loggerService.log(this.context, &#x60;[${actionName}]&#x60;);
      const data: any &#x3D; pattern.data;
      return await this.repository.findAllByQuery(data.query, data.projection);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.GENERATE_ACCESS_TOKEN_BY_USER })
    async generateAccessTokenByUser(@Body() pattern: any, actionName &#x3D; &#x27;generateAccessTokenByUser&#x27;) {
        this.loggerService.log(this.context, &#x60;[${actionName}]&#x60;);
        const data: any &#x3D; pattern.data;
        return await this.userDomainService.generateAccessToken(data);
    }

    @MessagePattern({ cmd: CmdPatternConst.LISTENER.CHANGE_ACTIVE_STATUS })
    async changeActiveStatus(@Body() pattern: any, actionName &#x3D; &#x27;changeActiveStatus&#x27;) {
        this.loggerService.log(this.context, &#x60;[${actionName}]&#x60;);
        const data: any &#x3D; pattern.data;
        return await this.userDomainService.changeActiveStatus(data);
    }
}
</code></pre>
    </div>
</div>









                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'controller';
            var COMPODOC_CURRENT_PAGE_URL = 'ListenerController.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
