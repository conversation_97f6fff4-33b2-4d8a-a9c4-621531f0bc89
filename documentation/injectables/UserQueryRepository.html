<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-sts documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-sts documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>UserQueryRepository</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/user.queryside/repository/query.repository.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#context">context</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkDuplicate">checkDuplicate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkDuplicateUpdate">checkDuplicateUpdate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkExistedById">checkExistedById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countAll">countAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create">create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#delete">delete</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#find">find</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAggregateModelById">findAggregateModelById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAll">findAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllByQuery">findAllByQuery</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAndUpdate">findAndUpdate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findByQuery">findByQuery</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findMany">findMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOne">findOne</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findUser">findUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findUserByEmail">findUserByEmail</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findUserByEmailOrPhone">findUserByEmailOrPhone</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findUserByEmailOrPhoneCare">findUserByEmailOrPhoneCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findUserById">findUserById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findUserByPermission">findUserByPermission</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findUserByRoleId">findUserByRoleId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findUserWithCodeAndUserId">findUserWithCodeAndUserId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findUserWithEmailAndPhone">findUserWithEmailAndPhone</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getAvaiableAccountants">getAvaiableAccountants</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getHierarchyCategories">getHierarchyCategories</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getListRolesByIds">getListRolesByIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getUser">getUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getUserByPermission">getUserByPermission</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getUserListByUserIds">getUserListByUserIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#hierarchy">hierarchy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isUserExisted">isUserExisted</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listAllByQuery">listAllByQuery</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#maskasEmployeeExpried">maskasEmployeeExpried</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#modifyUserPermission">modifyUserPermission</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#patch">patch</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#removeDeviceTokens">removeDeviceTokens</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#resetUserRole">resetUserRole</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#revolvePermissionByRole">revolvePermissionByRole</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#setUserPermission">setUserPermission</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#update">update</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateOneByQuery">updateOneByQuery</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updatePermissionByRole">updatePermissionByRole</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateUseWithEmail">updateUseWithEmail</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(queryModel: Model<IUserDocument>, loggerService: <a href="../injectables/MsxLoggerService.html">MsxLoggerService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="15" class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:15</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>queryModel</td>
                                                  
                                                        <td>
                                                                    <code>Model&lt;IUserDocument&gt;</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>loggerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MsxLoggerService.html" target="_self" >MsxLoggerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkDuplicate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkDuplicate
                        </b>
                        <a href="#checkDuplicate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkDuplicate(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="366"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:366</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkDuplicateUpdate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkDuplicateUpdate
                        </b>
                        <a href="#checkDuplicateUpdate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkDuplicateUpdate(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="380"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:380</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkExistedById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkExistedById
                        </b>
                        <a href="#checkExistedById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkExistedById(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="350"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:350</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countAll
                        </b>
                        <a href="#countAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countAll(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="610"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:610</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            create
                        </b>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(readmodel)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="226"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:226</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>readmodel</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IUserDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="delete"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            delete
                        </b>
                        <a href="#delete"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>delete(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="277"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:277</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="find"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            find
                        </b>
                        <a href="#find"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>find(query: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank">object</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="71"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:71</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IUserDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAggregateModelById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAggregateModelById
                        </b>
                        <a href="#findAggregateModelById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAggregateModelById(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="214"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:214</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;QueryAggregateModel&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAll
                        </b>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAll()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="51"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:51</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IUserDocument[]&gt;</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllByQuery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAllByQuery
                        </b>
                        <a href="#findAllByQuery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllByQuery(query, projection?: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="62"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:62</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>projection</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IUserDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAndUpdate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAndUpdate
                        </b>
                        <a href="#findAndUpdate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAndUpdate(model: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="420"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:420</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findByQuery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findByQuery
                        </b>
                        <a href="#findByQuery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findByQuery(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="58"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:58</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IUserDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findMany
                        </b>
                        <a href="#findMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findMany(filter, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="619"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:619</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>filter</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;findMany&#x27;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IUserDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOne"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findOne
                        </b>
                        <a href="#findOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOne(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="120"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:120</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findUser
                        </b>
                        <a href="#findUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findUser(query, system?)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="133"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:133</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>system</td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findUserByEmail"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findUserByEmail
                        </b>
                        <a href="#findUserByEmail"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findUserByEmail(username: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="433"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:433</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>username</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findUserByEmailOrPhone"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findUserByEmailOrPhone
                        </b>
                        <a href="#findUserByEmailOrPhone"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findUserByEmailOrPhone(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="22"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:22</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findUserByEmailOrPhoneCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findUserByEmailOrPhoneCare
                        </b>
                        <a href="#findUserByEmailOrPhoneCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findUserByEmailOrPhoneCare(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="35"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:35</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findUserById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findUserById
                        </b>
                        <a href="#findUserById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findUserById(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="150"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:150</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IUserDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findUserByPermission"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findUserByPermission
                        </b>
                        <a href="#findUserByPermission"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findUserByPermission(permission: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="489"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:489</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>permission</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findUserByRoleId"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findUserByRoleId
                        </b>
                        <a href="#findUserByRoleId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findUserByRoleId(roleId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="484"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:484</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>roleId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findUserWithCodeAndUserId"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findUserWithCodeAndUserId
                        </b>
                        <a href="#findUserWithCodeAndUserId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findUserWithCodeAndUserId(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, otp: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="519"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:519</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>otp</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findUserWithEmailAndPhone"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findUserWithEmailAndPhone
                        </b>
                        <a href="#findUserWithEmailAndPhone"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findUserWithEmailAndPhone(email: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, phone: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, system: string[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="447"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:447</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>email</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>phone</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>system</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAvaiableAccountants"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getAvaiableAccountants
                        </b>
                        <a href="#getAvaiableAccountants"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAvaiableAccountants()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="504"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:504</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any[]&gt;</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getHierarchyCategories"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getHierarchyCategories
                        </b>
                        <a href="#getHierarchyCategories"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getHierarchyCategories(sorted?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="287"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:287</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>sorted</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getListRolesByIds"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getListRolesByIds
                        </b>
                        <a href="#getListRolesByIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getListRolesByIds(ids: any[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="530"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:530</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>ids</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getUser
                        </b>
                        <a href="#getUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUser(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, fields?: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="480"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:480</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Description</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                    <td>
                                    </td>
                                </tr>
                                <tr>
                                    <td>fields</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                    <td>
                                        <p>{id: 1} </p>

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserByPermission"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getUserByPermission
                        </b>
                        <a href="#getUserByPermission"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserByPermission(permissions: any[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="527"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:527</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>permissions</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getUserListByUserIds"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getUserListByUserIds
                        </b>
                        <a href="#getUserListByUserIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getUserListByUserIds(userIds: string[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="499"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:499</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>userIds</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="hierarchy"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            hierarchy
                        </b>
                        <a href="#hierarchy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>hierarchy(array: any[], sorted?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="312"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:312</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>array</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>sorted</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isUserExisted"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            isUserExisted
                        </b>
                        <a href="#isUserExisted"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isUserExisted(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="201"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:201</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listAllByQuery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listAllByQuery
                        </b>
                        <a href="#listAllByQuery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listAllByQuery(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="571"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:571</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="maskasEmployeeExpried"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            maskasEmployeeExpried
                        </b>
                        <a href="#maskasEmployeeExpried"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>maskasEmployeeExpried(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="639"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:639</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="modifyUserPermission"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            modifyUserPermission
                        </b>
                        <a href="#modifyUserPermission"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>modifyUserPermission(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="249"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:249</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IUserDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="patch"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            patch
                        </b>
                        <a href="#patch"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>patch(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, action: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="634"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:634</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>action</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;patch&#x27;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeDeviceTokens"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            removeDeviceTokens
                        </b>
                        <a href="#removeDeviceTokens"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeDeviceTokens(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, deviceToken: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="625"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:625</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>deviceToken</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;removeDeviceTokens&#x27;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="resetUserRole"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            resetUserRole
                        </b>
                        <a href="#resetUserRole"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>resetUserRole(role)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="404"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:404</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>role</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="revolvePermissionByRole"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            revolvePermissionByRole
                        </b>
                        <a href="#revolvePermissionByRole"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>revolvePermissionByRole(roleId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="512"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:512</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>roleId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="setUserPermission"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            setUserPermission
                        </b>
                        <a href="#setUserPermission"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>setUserPermission(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="237"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:237</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IUserDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            update
                        </b>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="265"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:265</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;IUserDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateOneByQuery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateOneByQuery
                        </b>
                        <a href="#updateOneByQuery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateOneByQuery(user, query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="274"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:274</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updatePermissionByRole"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updatePermissionByRole
                        </b>
                        <a href="#updatePermissionByRole"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updatePermissionByRole(roleId, permissions, roleName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="472"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:472</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>roleId</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>permissions</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>roleName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateUseWithEmail"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateUseWithEmail
                        </b>
                        <a href="#updateUseWithEmail"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateUseWithEmail(email: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, otp: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>, token: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="516"
                            class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:516</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>email</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>otp</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>token</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="context"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            context</b>
                            <a href="#context"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>UserQueryRepository.name</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="15" class="link-to-prism">src/modules/user.queryside/repository/query.repository.ts:15</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Model, model } from &#x27;mongoose&#x27;;
import { Inject, Injectable } from &#x27;@nestjs/common&#x27;;
import { QueryAggregateModel } from &#x27;../models/query-aggregate.model&#x27;;
import { IUserDocument } from &#x27;../interfaces/document.interface&#x27;;
import { IResult } from &#x27;../../shared/interfaces/result.interface&#x27;;
import _ &#x3D; require(&#x27;lodash&#x27;);
import { CommonConst } from &#x27;../../shared/constant/common.const&#x27;;
import { MsxLoggerService } from &#x27;../../logger/logger.service&#x27;;
import { RoleEnum } from &#x27;../../shared/services/user/enum/role.enum&#x27;;
import {CommonUtils} from &quot;../../shared/classes/class-utils&quot;;
const clc &#x3D; require(&#x27;cli-color&#x27;);

@Injectable()
export class UserQueryRepository {
    private readonly context &#x3D; UserQueryRepository.name;
    constructor(
        @Inject(CommonConst.QUERY_MODEL_TOKEN)
        private readonly queryModel: Model&lt;IUserDocument&gt;,
        private readonly loggerService: MsxLoggerService
    ) { }

    async findUserByEmailOrPhone(query): Promise&lt;any&gt; {
        return await this.queryModel.findOne(
            {
                $or: [
                    { email: { $regex: new RegExp(&quot;^&quot; + query.email + &quot;$&quot;, &quot;i&quot;) } },
                    { phone: query.phone || &#x27;null&#x27; }
                ],
                system: { $in : [null, &#x27;&#x27;, &#x27;O2O&#x27;] }
            }
        ).then(result &#x3D;&gt; {
            return result;
        });
    }
    async findUserByEmailOrPhoneCare(query): Promise&lt;any&gt; {
        const _query &#x3D; {
          $or: [
            { email: { $regex: new RegExp(&quot;^&quot; + query.email + &quot;$&quot;, &quot;i&quot;) } },
            { phone: query.phone || &#x27;null&#x27; }
          ],
            system: &#x27;care&#x27;
        };
        if(query.id) {
          _query[&quot;id&quot;] &#x3D; { $ne: query.id };
        }
        return await this.queryModel.findOne(_query).then(result &#x3D;&gt; {
            return result;
        });
    }

    async findAll(): Promise&lt;IUserDocument[]&gt; {
        return await this.queryModel.find()
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }
    async findByQuery(query): Promise&lt;IUserDocument[]&gt; {
        return await this.queryModel.find(query);
    }

    async findAllByQuery(query, projection?: any): Promise&lt;IUserDocument[]&gt; {

        return await this.queryModel.find(query, projection)
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }

    async find(query: object): Promise&lt;IUserDocument&gt; {
        return await this.queryModel.aggregate([
            {
                &#x27;$match&#x27;: query
            }, {
                &#x27;$lookup&#x27;: {
                    &#x27;from&#x27;: &#x27;roles&#x27;,
                    &#x27;let&#x27;: {
                        &#x27;roleId&#x27;: &#x27;$roleId&#x27;
                    },
                    &#x27;pipeline&#x27;: [
                        {
                            &#x27;$match&#x27;: {
                                &#x27;$expr&#x27;: {
                                    &#x27;$and&#x27;: [
                                        {
                                            &#x27;$eq&#x27;: [
                                                &#x27;$$roleId&#x27;, &#x27;$id&#x27;
                                            ]
                                        }
                                    ]
                                }
                            }
                        }, {
                            &#x27;$project&#x27;: {
                                &#x27;name&#x27;: 1
                            }
                        }
                    ],
                    &#x27;as&#x27;: &#x27;role&#x27;
                }
            }
        ])
        .exec()
        .then((response) &#x3D;&gt; {
            if (_.isArray(response)) {
                response.forEach(res &#x3D;&gt; {
                    delete res.password;
                });
                return response;
            } else {
                return [];
            }
        })
        .catch((exp) &#x3D;&gt; {
            return exp;
        });
    }

    async findOne(query): Promise&lt;any&gt; {
        this.loggerService.log(this.context, &#x27;UserQueryRepository/findOne/query &#x3D;&gt;&#x27;, query);
        return await this.queryModel.findOne(query)
            .exec()
            .then((response) &#x3D;&gt; {
                // this.loggerService.log(this.context, &#x27;response&#x27;, response);
                return response;
            }).catch((err) &#x3D;&gt; {
                this.loggerService.log(this.context, &#x27;error&#x27;, err);
                return err;
            });
    }

    async findUser(query, system?): Promise&lt;any&gt; {
        const _query: any &#x3D; {
            $or: [
                { email: { $regex: new RegExp(&quot;^&quot; + query + &quot;$&quot;, &quot;i&quot;) } },
                { phone: query || &#x27;null&#x27; }
            ],
            system: { $in : [null, &#x27;&#x27;, &#x27;O2O&#x27;] }
        };
        if (system) {
            _query.system &#x3D; system;
        }
        return await this.queryModel.findOne(_query).then(result &#x3D;&gt; {
            return result;
        });

    }

    async findUserById(id: string): Promise&lt;IUserDocument&gt; {
        this.loggerService.log(this.context, &#x27;findUserById &#x3D;&gt;&#x27;, id);
        return await this.queryModel.aggregate([
            {
                &#x27;$match&#x27;: {
                    &#x27;id&#x27;: id
                }
            }, {
                &#x27;$lookup&#x27;: {
                    &#x27;from&#x27;: &#x27;roles&#x27;,
                    &#x27;let&#x27;: {
                        &#x27;roleId&#x27;: &#x27;$roleId&#x27;
                    },
                    &#x27;pipeline&#x27;: [
                        {
                            &#x27;$match&#x27;: {
                                &#x27;$expr&#x27;: {
                                    &#x27;$and&#x27;: [
                                        {
                                            &#x27;$eq&#x27;: [
                                                &#x27;$$roleId&#x27;, &#x27;$id&#x27;
                                            ]
                                        }
                                    ]
                                }
                            }
                        }, {
                            &#x27;$project&#x27;: {
                                &#x27;name&#x27;: 1,
                                &#x27;routerLink&#x27;: 1
                            }
                        }
                    ],
                    &#x27;as&#x27;: &#x27;role&#x27;
                }
            }
        ])
            .exec()
            .then((response) &#x3D;&gt; {
                if (_.isArray(response)) {
                    delete response[0].password;
                    return response.length &gt; 0 ? response[0] : {}
                }
                delete response.password;
                return response;
            })
            .catch((exp) &#x3D;&gt; {
                return exp;
            });
    }

    async isUserExisted(id: string): Promise&lt;boolean&gt; {
        return this.queryModel.find(id).limit(1)
            .exec()
            .then((response) &#x3D;&gt; {
                if (response &amp;&amp; response.length &gt;&#x3D; 1)
                    return true;
                return false;
            })
            .catch((exp) &#x3D;&gt; {
                return exp;
            });
    }

    async findAggregateModelById(id: string): Promise&lt;QueryAggregateModel&gt; {
        return await this.queryModel.findOne({ id })
            .exec()
            .then((response) &#x3D;&gt; {
                this.loggerService.log(this.context, &#x27;findAggregateModelById user query side&#x27;);
                return new QueryAggregateModel(id);
            })
            .catch((exp) &#x3D;&gt; {
                return exp;
            });
    }

    async create(readmodel): Promise&lt;IUserDocument&gt; {
        return await this.queryModel.create(readmodel)
            .then((response) &#x3D;&gt; {
                this.loggerService.log(this.context, &#x27;createEvent User at query side&#x27;);
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }


    async setUserPermission(model): Promise&lt;IUserDocument&gt; {
        this.loggerService.log(this.context, &#x27;setUserPermission &#x3D;&gt;&#x27;, model);
        return this.queryModel.updateOne({ id: model.id }, _.isEmpty(model.roleId) ? { roleId: null, permissions: [] } : { permissions: model.permissions, roleId: model.roleId })
            .then((response) &#x3D;&gt; {
                this.loggerService.log(this.context, &#x27;setUserPermission at query side&#x27;, response);
                return response;
            }).catch((error) &#x3D;&gt; {
                this.loggerService.log(this.context, &#x27;error &#x3D;&gt;&#x27;, error);
                return error;
            });
    }

    async modifyUserPermission(model): Promise&lt;IUserDocument&gt; {
        return await this.queryModel.update(
            {
                roles: { $elemMatch: { roleId: model.roleId } }
            },
            { $set: { &#x27;roles.$.permissions&#x27;: model.permissions } }
        )
            .then((response) &#x3D;&gt; {
                return response;
            })
            .catch((error) &#x3D;&gt; {
                return error;
            });
    }


    async update(model): Promise&lt;IUserDocument&gt; {
        return await this.queryModel.updateOne({ id: model.id }, model)
            .then((response) &#x3D;&gt; {
                this.loggerService.log(this.context, &#x27;updated User at query side&#x27;);
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }
    async updateOneByQuery(user, query): Promise&lt;any&gt; {
      await this.queryModel.updateOne({id: user.id}, query);
    }
    async delete(model): Promise&lt;any&gt; {
        return await this.queryModel.deleteOne({ id: model.id })
            .then((response) &#x3D;&gt; {
                this.loggerService.log(this.context, &#x27;Delete User at query side&#x27;);
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async getHierarchyCategories(sorted?: boolean): Promise&lt;any&gt; {

        let result: IResult &#x3D; {
            err: {},
            data: [],
            msg: &#x27;&#x27;
        };

        return await this.queryModel.find()
            .exec()
            .then((categories) &#x3D;&gt; {

                const data &#x3D; this.hierarchy(categories, sorted);
                result &#x3D; {
                    err: {},
                    data,
                    msg: &#x27;Hierarchy User by Parent.&#x27;
                };
                return result;
            })
            .catch((error) &#x3D;&gt; {
                throw error;
            });
    }

    private hierarchy(array: any[], sorted?: boolean) {
        const map &#x3D; { hierarchyMap: [] };
        // map.hierarchyMap &#x3D; [];
        const hierarchyMap &#x3D; [];
        // tslint:disable-next-line:prefer-for-of
        for (let i &#x3D; 0; i &lt; array.length; i++) {
            const obj &#x3D; array[i];

            if (!(obj.id in map)) {
                map[obj.id] &#x3D; obj;
                map[obj.id].children &#x3D; [];
            }

            const parentId &#x3D; obj.parentId || &#x27;-&#x27;;
            if (!(parentId in map)) {
                map[parentId] &#x3D; {};
                map[parentId].children &#x3D; [];
            }

            const hierarchyNameBuilder &#x3D;
                (typeof map[parentId].hierarchyName &#x3D;&#x3D;&#x3D; &#x27;undefined&#x27;
                    ? &#x27;&#x27;
                    : map[parentId].hierarchyName + &#x27; &gt;&gt; &#x27;) + obj.name;
            map[obj.id].hierarchyName &#x3D; hierarchyNameBuilder;
            map[parentId].children.push(map[obj.id]);

            hierarchyMap.push({ hierarchyName: hierarchyNameBuilder, id: obj.id });

        }

        let sorting &#x3D; hierarchyMap;
        if (sorted) {
            sorting &#x3D; _.orderBy(hierarchyMap, [&#x27;hierarchyName&#x27;], [&#x27;asc&#x27;]);
        }

        return sorting;
    }

    async checkExistedById(query): Promise&lt;boolean&gt; {

        return await this.queryModel.aggregate(
            [
                { $match: { id: query.id } }
            ])
            .exec()
            .then((result) &#x3D;&gt; {
                if (result &amp;&amp; result.length &gt; 0) {
                    return true;
                }
                return false;
            });
    }


    async checkDuplicate(query): Promise&lt;boolean&gt; {
        return await this.queryModel.aggregate(
            [
                { $match: query }
            ])
            .exec()
            .then((result) &#x3D;&gt; {
                if (result &amp;&amp; result.length &gt; 0) {
                    return true;
                }
                return false;
            });
    }

    async checkDuplicateUpdate(query): Promise&lt;boolean&gt; {
        return await this.queryModel.aggregate(
            [
                {
                    $match: {
                        $and: [
                            { name: query.name },
                            { id: { $ne: query.id } }
                        ]
                    }
                }
            ])
            .exec()
            .then((result) &#x3D;&gt; {
                if (result &amp;&amp; result.length &gt; 0) {
                    return true;
                }
                return false;
            })
            .catch((error) &#x3D;&gt; {
                throw error;
            });
    }

    async resetUserRole(role) {
        return await this.queryModel.updateMany(
            {
                roles: { $elemMatch: { roleId: role.roleId } }
            },
            { $set: { &#x27;roles.$.permissions&#x27;: role.permissions } } ,
        )
            .then((response) &#x3D;&gt; {
                return response;
            })
            .catch((error) &#x3D;&gt; {
                this.loggerService.error(this.context, &#x27;find/resetUserRole/error &#x3D;&gt;&#x27;, error);
                return error;
            });
    }

    async findAndUpdate(model: any) {
        return await this.queryModel.findOneAndUpdate(
            { id: model.id },
            model,
            { upsert: true }
        ).exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async findUserByEmail(username: string) {
        let user;
        return await this.queryModel.find()
            .exec()
            .then(result &#x3D;&gt; {
                const filter &#x3D; _.filter(result, (o: IUserDocument) &#x3D;&gt; {
                    return (o.email.toLocaleLowerCase() &#x3D;&#x3D;&#x3D; username.toLowerCase() || o.phone.toLocaleLowerCase() &#x3D;&#x3D;&#x3D; username.toLowerCase());
                });
                if (filter.length &gt; 0) {
                    user &#x3D; filter[0];
                }
                return user;
            });
    }
    async findUserWithEmailAndPhone(email: string, phone: string, system: string[]): Promise&lt;any&gt; {
        return await this.queryModel.aggregate([
            {
                $match: {
                    $or: [
                        { &quot;email&quot;: email },
                        { &quot;phone&quot;: phone }
                    ],
                    system: { $in : system }
                }
            },
            {
                $project: {
                    email: { $eq: [&quot;$email&quot;, { $literal: email }] },
                    phone: { $eq: [&quot;$phone&quot;, { $literal: phone }] },
                    _id: 0
                }
            }
        ]).allowDiskUse(true);
    }
    /**
     *
     * @param {String} roleId
     * @param permissions
     */
    async updatePermissionByRole(roleId, permissions, roleName: string) {
        return await this.queryModel.updateMany({ roleId }, { $set: { permissions } })
    }
    /**
     * 
     * @param {String} userId  
     * @param {Object} fields {id: 1} 
     */
    async getUser(userId: string, fields?: any) {
        return this.queryModel.findById(userId, fields).exec();
    }

    async findUserByRoleId(roleId: string): Promise&lt;any[]&gt; {
        return await this.queryModel.find({ roleId: {$exists: true, $nin: [null, &#x27;&#x27;], $eq: roleId} })
            .exec();
    }

    async findUserByPermission(permission: string): Promise&lt;any[]&gt; {
        return await this.queryModel.find({ 
            permissions: {
                $elemMatch: {
                    featureName: permission
                }
            } 
        }).exec();
    }
    
    async getUserListByUserIds(userIds: string[]) {
        return await this.queryModel.find({ id: { $in: userIds } })
            .exec();
    }

    async getAvaiableAccountants(): Promise&lt;any[]&gt; {
        return await this.queryModel.find({ $or: [{roleId: RoleEnum.KE_TOAN}, {roleId: RoleEnum.KE_TOAN_TRUONG}], active: true }, {id: 1, email: 1, name: 1})
            .exec();
    }
    /**
     * 
     * @param roleId 
     */
    async revolvePermissionByRole(roleId: string) {
        return await this.queryModel.updateMany({ roleId }, { $set: { permissions: [], roleId: null } })
    }

    async updateUseWithEmail(email: string, otp:String, token: String) {
        return await this.queryModel.update( {&quot;email&quot;:email, system: { $in : [null, &#x27;&#x27;, &#x27;O2O&#x27;] } },{$set: { &quot;otp&quot;: otp }})
    }
    async findUserWithCodeAndUserId(userId: string, otp: string) {
        return await this.queryModel.findOne(
            {
                id: userId,
                otp:otp
            }
        ).exec();
    }
    async getUserByPermission(permissions: any[]) {
        return await this.queryModel.find({ permissions }).exec()
    }
    async getListRolesByIds(ids: any[]) {
        return await this.queryModel.aggregate(
            [
                {
                    $match: {
                             id: {$in: ids} 
                    }
                },
                {
                    $lookup: {
                        from: &#x27;roles&#x27;,
                        let: { roleId: &#x27;$roleId&#x27; },
                        pipeline: [
                            {
                                $match:
                                {
                                    $expr:
                                    {
                                        $and:
                                            [
                                                { $eq: [&#x27;$$roleId&#x27;, &#x27;$id&#x27;] },
                                            ]
                                    }
                                }
                            },
                        ],
                        as: &#x27;roles&#x27;
                    }
                },
                {
                    $unwind: &#x27;$roles&#x27;
                },
                {
                    $project: {
                        id: &#x27;$$ROOT.id&#x27;,
                        roles: &#x27;$$ROOT.roles.name&#x27;,
                    }
                },
            ]).allowDiskUse(true);
    }

    async listAllByQuery(query: any &#x3D; {}) {
        const project: any &#x3D; { _id: 0 };
        if (!_.isEmpty(query._fields)) {
            const fields &#x3D; query._fields.split(&#x27;,&#x27;);
            fields.forEach((f) &#x3D;&gt; {
                project[f.trim()] &#x3D; 1;
            });
            delete query._fields;
        }
        let sort: any &#x3D; {
            createdDate: 1,
        };
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; CommonUtils.transformSort(query.sort) || {
                createdDate: 1,
            };
            delete query.sort;
        }
        let aggregate: any &#x3D; [
            { $project: project },
            { $sort: sort },
        ];
        if (query.isPaging) {
            const page &#x3D; query.page;
            const pageSize &#x3D; query.pageSize;
            delete query.isPaging;
            delete query.page;
            delete query.pageSize;
            aggregate.push({ $skip: Math.floor(pageSize * page - pageSize) }, { $limit: pageSize });
        }

        aggregate &#x3D; [{ $match: query }].concat(aggregate);
        return await this.queryModel
          .aggregate(aggregate)
          .exec()
          .then((res) &#x3D;&gt; {
              return res;
          });
    }
    async countAll(query: any &#x3D; {}) {
        delete query.isPaging;
        delete query.page;
        delete query.pageSize;
        delete query.sort;
        return await this.queryModel.countDocuments(query)
          .exec();
    }

    async findMany(filter, actionName &#x3D; &#x27;findMany&#x27;): Promise&lt;IUserDocument[]&gt; {
      this.loggerService.log(this.context, actionName, filter);
      const { where, sort, skip, limit, projection } &#x3D; filter;
      return this.queryModel.find(where, projection).sort(sort).skip(skip).limit(limit);
    }

    async removeDeviceTokens(query: any, deviceToken: string, actionName &#x3D; &#x27;removeDeviceTokens&#x27;) {
      this.loggerService.log(this.context, clc.yellow(&#x60;[${actionName}]&#x60;));
      if (query &amp;&amp; deviceToken) {
        return await this.queryModel.updateMany(query, { $pull: { deviceTokens: deviceToken } });
      } else {
        return null;
      }
    }

    async patch(query: any, action: any, actionName &#x3D; &#x27;patch&#x27;): Promise&lt;any&gt; {
      this.loggerService.log(this.context, clc.yellow(&#x60;[${actionName}]&#x60;));
      return await this.queryModel.updateOne(query, action);
    }

    async maskasEmployeeExpried(query) {
        return await this.queryModel.updateMany(query, { $set: { active: false }}).exec();
    }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'UserQueryRepository.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
