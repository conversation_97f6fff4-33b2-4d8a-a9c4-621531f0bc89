import * as mongoose from "mongoose";
import { DataAreaEnum } from "../../../../shared-modules";
const uuid = require("uuid");
const beautifyUnique = require("mongoose-beautiful-unique-validation");

const JobTitleSchema = new mongoose.Schema(
  {
    type: { type: Number },
    jobTitleCode: { type: String },
    jobTitleName: { type: String },
    orgCode: { type: String },
    startDate: { type: String },
    endDate: { type: String },
  },
  { _id: false }
);

const FeatureSchema = new mongoose.Schema(
  {
    featureId: { type: String },
    featureName: { type: String },
    msxId: { type: String },
    msxName: { type: String },
  },
  { _id: false }
);

const FunctionRoleSchema = new mongoose.Schema(
  {
    name: { type: Array },
    features: { type: [FeatureSchema] },
  },
  { _id: false }
);

const DataRoleSchema = new mongoose.Schema(
  {
    name: { type: String },
    code: { type: String },
    type: {
      type: String,
      enum: Object.values(DataAreaEnum),
    },
    source: { type: String },

    // add new 20241126: case new role from ADM - isDelete = false (masterdata-orchestrator handled this)
    isDelete: { type: Boolean, default: true }
  },
  { _id: false }
);

const OrgScopeSchema = new mongoose.Schema(
  {
    orgCode: { type: String },
    orgName: { type: String },
    includeChid: { type: Boolean },
  },
  { _id: false }
);

const RoleSchema = new mongoose.Schema(
  {
    name: { type: String },
    code: { type: String },
    orgCode: { type: String },
    orgName: { type: String },
    orgScope: { type: [OrgScopeSchema] },
    type: { type: Number },
    active: { type: Boolean, default: true },
    dataRoles: { type: [DataRoleSchema] },
    functionRoles: { type: [FunctionRoleSchema] },

    // add new 20241126: case new role from ADM - isDelete = false, isAdmSource = true (masterdata-orchestrator handled this)
    isDelete: { type: Boolean, default: true },
    isAdmSource: { type: Boolean, default: false }
  },
  { _id: false }
);

const PayloadSchema = new mongoose.Schema({
  _id: { type: String, default: uuid.v4 },
  id: { type: String, default: uuid.v4, index: true },
  username: { type: String, index: true },

  // defined by BA
  isAdmin: { type: Boolean, default: false },
  effectiveDate: { type: String },
  companies: { type: Array },
  phone: { type: String, default: "" },
  gender: { type: String, default: "" },
  code: { type: String, index: true }, // generated by admin ss
  identity: { type: String, default: "" },
  avatar: { type: String, default: "" },
  avatarThumbnailUrl: { type: String, default: "" },
  dob: { type: String, default: "" },
  isActive: { type: Boolean, default: true },

  // receive from admin subsystem
  referCode: { type: String },
  fullName: { type: String, index: true },
  email: { type: String },
  jobTitles: { type: [JobTitleSchema] },
  roles: { type: [RoleSchema] },
  orgCode: { type: String },
  orgName: { type: String },
  accountType: { type: String },
  position: { type: String },

  // changed to map with ADM
  status: { type: Number, default: 0, index: true },

  modifiedDate: { type: Date, default: () => Date.now() },
  modifiedBy: { type: String, default: null },

  createdDate: { type: Date, default: () => Date.now() },
  eventName: { type: String },
  actionName: { type: String },
  revision: { type: Number, index: true },
});

PayloadSchema.pre("save", function (next) {
  this._id = this.get("id");
  next();
});

// Enable beautifying on this schema
PayloadSchema.plugin(beautifyUnique);
// ============

export const CqrsDomainSchema = new mongoose.Schema({
  _id: { type: String, default: uuid.v4 },
  id: { type: String, default: uuid.v4, index: true },
  streamId: { type: String, index: true },
  aggregate: String,
  aggregateId: String,
  eventName: { type: String, index: true },
  context: String,
  streamRevision: Number,
  commitId: String,
  commitSequence: Number,
  commitStamp: { type: Date, default: () => Date.now() },
  payload: { type: PayloadSchema },
});

CqrsDomainSchema.pre("save", function (next) {
  this._id = this.get("id");
  next();
});
