import { EventPublisher, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { ModifyPermissionCommand } from '../impl/modify-permission.cmd';
import { PermissionEventStreamRepository } from '../../repository/event-stream.repository';
import { AggregateModel } from '../../models/aggregate.model';
import { RoleQueryRepository } from '../../../role.queryside/repository/query.repository';
import { BaseEventStream } from '../../../shared/eventStream/models/base-event-stream.model';
import { isNullOrUndefined } from 'util';
import { CommonConst } from '../../../shared/constant/common.const';
import { MsxLoggerService } from '../../../logger/logger.service';

@CommandHandler(ModifyPermissionCommand)
export class ModifyPermissionCommandHandler implements
    ICommandHandler<ModifyPermissionCommand>{
    private readonly context = ModifyPermissionCommandHandler.name;
    private readonly aggregateName: string = CommonConst.AGGREGATES.PERMISSION.NAME;
    private readonly eventName: string = CommonConst.AGGREGATES.PERMISSION.MODIFIED;

    constructor(
        private readonly repository: PermissionEventStreamRepository,
        private readonly publisher: EventPublisher,
        private readonly roleQueryRepository: RoleQueryRepository,
        private readonly loggerService: MsxLoggerService
    ) { }

    // execute Cteate Role Command
    async execute(command: ModifyPermissionCommand) {
        this.loggerService.log(this.context, `Async Create ${this.aggregateName} cmd ...`);

        const { messagePattern, id, commandModel } = command;
        const obj: any = commandModel;
        
        const roleExisted = await this.roleQueryRepository.checkExistedById({
            id: obj.roleId
        });
        if (!roleExisted) {
            return;
        }

        const es = await this.repository.findEventStreamById(commandModel.id);

        /**
         * this code block only works for payload relationship with other entities
         */
        if (es && es.payload) {

            const payload: any = es.payload;
            // if existed object and have no new one
            // keep existing object
            if (payload) {
                commandModel.id = es.streamId;
                commandModel.payload.name = (commandModel.payload.name || payload.name);
                commandModel.description = (commandModel.description || payload.description);
                commandModel.active = (commandModel.active || payload.active);
                commandModel.softDelete = (commandModel.softDelete || payload.softDelete);
                commandModel.modifiedBy = (commandModel.modifiedBy || payload.modifiedBy);
            }
        }

        const eventStream = new BaseEventStream();
        eventStream.id = id;
        eventStream.aggregateId = id;

        if (isNullOrUndefined(commandModel.id)) commandModel.id = id;
        eventStream.streamId = commandModel.id;

        eventStream.aggregate = this.aggregateName;
        eventStream.eventName = this.eventName;

        commandModel.eventName = this.eventName;
        commandModel.actionName = messagePattern;

        // aggregate model
        const aggregateModel = new AggregateModel(eventStream);
        // pulisher
        const cmdPublisher = this.publisher.mergeObjectContext(aggregateModel);
        const msg = this.aggregateName + '.' + messagePattern;
        cmdPublisher.addItem(msg, commandModel);
        cmdPublisher.commit();
    }
}
