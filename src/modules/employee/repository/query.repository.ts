import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { IEmployeeDocument } from '../interfaces/document.interface';
import _ = require('lodash');
import { EmployeeClient } from '../../mgs-sender/employee.client';
import { BaseRepository, CmdPatternConst } from '../../../../shared-modules';
import { CommonConst } from '../../shared/constant/common.const';

@Injectable()
export class EmployeeQueryRepository extends BaseRepository<IEmployeeDocument> {

  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<IEmployeeDocument>,
    private readonly employeeClient: EmployeeClient,
  ) {
    super(readModel);
  }

  async find(query, viewQuery): Promise<any> {

    return await this.readModel.find(query, viewQuery)
      .exec()
      .then(result => {
        // console.log('employee find/res =>', result);
        return result;
      });
  }

  async findOneById(id, viewQuery): Promise<any> {
    return await this.readModel.findOne({ id }, viewQuery)
      .exec()
      .then(result => {
        // console.log('employee find/res =>', result);
        return result;
      });
  }

  async updateOne(model: any) {

    return await this.readModel.updateOne(
      { id: model.id },
      model,
      { upsert: true }
    ).exec()
      .then((response) => {
        return response;
      }).catch((error) => {
        return error;
      });
  }
  async saveAll(models: any[]): Promise<any> {
    if (models && models.length) {
      return await this.readModel.bulkWrite(
        models.map((model) => {
          return {
            updateOne: {
              filter: { id: model.id },   // Detect Update by id
              update: { $set: model },
              upsert: true,
            }
          };
        })
      );
    } else {
      return [];
    }
  }

  async prepareData(query) {
    const lastSyncEmp = await this.readModel.find().sort({ lastSync: 'desc' }).limit(1);
    const lastSync = lastSyncEmp.length > 0 ? lastSyncEmp[0]['lastSync'] : null;
    const lastSyncDate = lastSync instanceof Date ? lastSync : null;

    const today = new Date();
    if (lastSyncDate === null || today.getDate() != lastSyncDate.getDate() ||
      today.getMonth() != lastSyncDate.getMonth() ||
      today.getFullYear() != lastSyncDate.getFullYear()) {
      const employees = await this.employeeClient.sendDataPromise({}, CmdPatternConst.EMPLOYEE.DROPDOWN);
      const empCount = await this.readModel.countDocuments();
      if (employees.length != empCount) {
        const updates = employees.map((emp) => {
          if (emp.id) {
            return {
              updateOne: {
                filter: { id: emp.id },
                update: { $set: { ...emp, name: emp.name || emp.nameVN, accountId: emp.accountId, lastSync: today } },
                upsert: true
              }
            };
          }
          return null;
        }).filter(Boolean);

        if (updates.length > 0) {
          await this.readModel.bulkWrite(updates);
        } else {
          console.log("No updates emp to perform.");
        }
      }
    }
  }

  async getDropdown(user, query) {
    const { search = '', id = '', isAll } = query;

    const match = {};
    let aggregate: any[] = [];
    if (!_.isEmpty(search)) {
      match['$or'] = [
        { name: { $regex: new RegExp(search), $options: 'i' } },
        { email: { $regex: new RegExp(search), $options: 'i' } },
        { username: { $regex: new RegExp(search), $options: 'i' } }
      ];
    }

    if (!_.isEmpty(id)) {
      match['id'] = { $in: id.split(',') };
    }

    // check orgCode
    if (isAll === 'false') {
      match['orgCode'] = user.orgCode;
    }

    console.log('getDropdown', match);

    // join accounts collection
    aggregate.push({
      $lookup: {
        from: 'accounts',
        localField: 'accountId',
        foreignField: 'id',
        as: 'accountInfo'
      }
    });

    // get list account
    aggregate.push({
      $unwind: {
        path: '$accountInfo',
        preserveNullAndEmptyArrays: true
      }
    });
    aggregate.push({
      $project: {
        _id: 0,
        id: 1,
        code: 1,
        name: 1,
        email: 1,
        accountId: '$accountInfo.id',
        username: '$accountInfo.username',
        phoneNumber: '$accountInfo.phone',
        cccd: '$accountInfo.identity',
        cmnd: '$accountInfo.identity',
        dvbh: '$accountInfo.orgName',
        orgCode: '$accountInfo.orgCode',
        posId: '$pos.id',
        posName: '$pos.name'
      }
    });
    aggregate.push({
      $match: match
    });

    if (query.page || query.pageSize) {
      let pageSize: number = parseInt(query["pageSize"]) || 10;
      let page: number = parseInt(query["page"]) || 1;
      aggregate.push({
        $facet: {
          rows: [
            { $skip: Math.floor(pageSize * page - pageSize) },
            { $limit: pageSize },
          ],
          total: [
            {
              $count: "count",
            },
          ],
        },
      });

      return this.readModel
        .aggregate(aggregate)
        .allowDiskUse(true)
        .exec()
        .then((result) => {
          const total = result[0].total[0] ? result[0].total[0].count : 0;
          return {
            page,
            pageSize,
            total: total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize),
            rows: result[0].rows,
          };
        });
    } else {
      return this.readModel
        .aggregate(aggregate)
        .allowDiskUse(true)
        .exec()
        .then((res) => {
          return res;
        });
    }
  }

  async getDropdownByIds(ids) {
    const result = await this.readModel.aggregate([
      {
        $match: { id: { $in: ids } }
      }, {
        $project: {
          _id: 0,
          id: 1,
          name: 1,
          email: 1
        }
      }
    ]);
    return result;
  }
}
