import { AggregateRoot } from '@nestjs/cqrs';
import { MsxEventStreamCreated } from '../events/event-stream-created.evt';
import { BaseEventStream } from '../../shared/eventStream/models/base-event-stream.model';
import { CommandModel } from '../../shared/eventStream/models/command.model';


export class AggregateModel extends AggregateRoot {

    private payload;
    constructor(private readonly baseEventStream: BaseEventStream) {
        super();
    }

    addItem(messagePattern: string, commandModel?: CommandModel) {

        this.apply(new MsxEventStreamCreated(this.baseEventStream, commandModel, messagePattern));
    }
}
