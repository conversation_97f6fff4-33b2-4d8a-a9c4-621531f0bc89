import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { QueryAggregateModel } from '../models/query-aggregate.model';
import { IQueryDocument } from '../interfaces/document.interface';
import { IResult } from '../../shared/interfaces/result.interface';
import _ = require('lodash');
import { IPermission } from '../../shared/services/permission/interfaces/permission.interface';
import { CommonConst } from '../../shared/constant/common.const';

@Injectable()
export class PermissionQueryRepository {

    constructor(
        @Inject(CommonConst.QUERY_MODEL_TOKEN)
        private readonly readModel: Model<IQueryDocument>
    ) { }

    async findAll(): Promise<IQueryDocument[]> {

        return await this.readModel.find()
            .exec()
            .then(result => {
                return result;
            });
    }

    async findAlltest(): Promise<IQueryDocument[]> {

        return await this.readModel.find()
            .exec()
            .then(result => {
                return result;
            });
    }

    async getPermissionsByRoles(query): Promise<IQueryDocument[]> {

        const roleIds = query.roles.map(a => a.roleId);
        return await this.readModel.aggregate(
            [
                {
                    $match: { roleId: { $in: roleIds } }
                }
            ])
            .exec()
            .then((result) => {
                _.map(query.roles, (role) => {
                    return _.assign(role, _.find(result, { roleId: role.roleId }));
                });

                return query.roles;
            })
            .catch((error) => {
                throw error;
            });
    }

    async findPermissionsByRoles(query): Promise<IQueryDocument[]> {

        const roleIds = query.roles.map(a => a.roleId);

        return await this.readModel.aggregate(
            [
                {
                    $match: { roleId: { $in: roleIds } }
                }
            ])
            .exec()
            .then((result) => {


                if (result && result.length > 0) {
                    return result;
                }
                return null;
            })
            .catch((error) => {
                throw error;
            });
    }

    async findOne(query): Promise<IQueryDocument> {
        return await this.readModel.findOne(query)
            .exec()
            .then(result => {
                return result;
            });
    }
    async findRoleById(id: string): Promise<IQueryDocument> {
        return await this.readModel.findOne({ id })
            .exec()
            .then((response) => {
                return response;
            })
            .catch((exp) => {
                return exp;
            });
    }
    
    async getAcl(msxName: string): Promise<any> {
        return await this.readModel.find(
            { 'permissions.msxName': { $eq: msxName } }
        )
            .exec()
            .then((response: IPermission[]) => {
                console.log('getAcl/response =>', response);
                const obj = [];
                if (response && response.length > 0) {
                    _(response).forEach((role) => {
                        _(role.permissions).forEach((permission) => {
                            if (permission.msxName === msxName) {
                                // let acl = obj.find( (a) => a.resource === permission.featureName );
                                // if (isNullOrUndefined( acl)){
                                    _.forOwn(permission.action, (value, key) => {
                                        const acl = {
                                            role: permission.featureName,
                                            resource: permission.featureName,
                                            // attributes: '[*]'
                                        };
                                        if (value.toString() === 'true') {
                                            // tslint:disable-next-line:no-string-literal
                                            acl['action'] = key;
    
                                            obj.push(acl);
                                        }
                                    });

                                // }
                            }

                        });
                    });
                }

                return obj;
            })
            .catch((exp) => {
                return exp;
            });
    }

    async findAggregateModelById(id: string): Promise<QueryAggregateModel> {
        return await this.readModel.findOne({ id })
            .exec()
            .then((response) => {
                return new QueryAggregateModel(id);
            })
            .catch((exp) => {
                return exp;
            });
    }

    async setPermission(readmodel): Promise<IQueryDocument> {
        
        return await this.readModel.findOneAndUpdate(
            { roleId: readmodel.roleId },
            readmodel,
            { upsert: true })
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    async getHierarchyCategories(sorted?: boolean): Promise<any> {

        let result: IResult = {
            err: {},
            data: [],
            msg: ''
        };

        return await this.readModel.find()
            .exec()
            .then((categories) => {

                const data = this.hierarchy(categories, sorted);
                result = {
                    err: {},
                    data,
                    msg: 'Hierarchy Role by Parent.'
                };
                return result;
            })
            .catch((error) => {
                throw error;
            });
    }
    private hierarchy(array: any[], sorted?: boolean) {
        const map = { hierarchyMap: [] };
        // map.hierarchyMap = [];
        const hierarchyMap = [];
        // tslint:disable-next-line:prefer-for-of
        for (let i = 0; i < array.length; i++) {
            const obj = array[i];

            if (!(obj.id in map)) {
                map[obj.id] = obj;
                map[obj.id].children = [];
            }

            // if (typeof map[obj.id].name == 'undefined') {
            //     map[obj.id].id = obj.id;
            //     map[obj.id].name = obj.name;
            //     // map[obj.id].attr = obj.attr
            //     map[obj.id].parentId = obj.parentId;
            // }

            const parentId = obj.parentId || '-';
            if (!(parentId in map)) {
                map[parentId] = {};
                map[parentId].children = [];
            }

            const hierarchyNameBuilder =
                (typeof map[parentId].hierarchyName === 'undefined'
                    ? ''
                    : map[parentId].hierarchyName + ' >> ') + obj.name;
            map[obj.id].hierarchyName = hierarchyNameBuilder;
            map[parentId].children.push(map[obj.id]);

            hierarchyMap.push({ hierarchyName: hierarchyNameBuilder, id: obj.id });

        }

        let sorting = hierarchyMap;
        if (sorted) {
            sorting = _.orderBy(hierarchyMap, ['hierarchyName'], ['asc']);
        }

        return sorting;
        // return map['-'];
    }

    async checkDuplicate(query): Promise<boolean> {

        return await this.readModel.aggregate(
            [
                { $match: { name: query.name } }
            ])
            .exec()
            .then((result) => {
                if (result && result.length > 0) {
                    return true;
                }
                return false;
            });
    }

    async checkDuplicateUpdate(query): Promise<boolean> {

        return await this.readModel.aggregate(
            [
                {
                    $match: {
                        $and: [
                            { name: query.name },
                            { id: { $ne: query.id } }
                        ]
                    }
                }
            ])
            .exec()
            .then((result) => {
                if (result && result.length > 0) {
                    return true;
                }
                return false;
            })
            .catch((error) => {
                throw error;
            });
    }
}
