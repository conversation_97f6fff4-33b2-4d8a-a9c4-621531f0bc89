import { EventPublisher, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { DeactiveRoleQueryCommand } from '../impl/deactive-query.cmd';
import { RoleQueryRepository } from '../../repository/query.repository';
import { CommonConst } from '../../../shared/constant/common.const';
import { AwesomeLogger } from '../../../../../shared-modules';

@CommandHandler(DeactiveRoleQueryCommand)
export class DeactiveRoleQueryCommandHandler implements ICommandHandler<DeactiveRoleQueryCommand> {
  private readonly logger = new AwesomeLogger(DeactiveRoleQueryCommandHandler.name);
  private readonly aggregateName: string = CommonConst.AGGREGATES.ROLE.NAME;
  constructor(
    private readonly repository: RoleQueryRepository,
    private readonly publisher: EventPublisher,
  ) {}

  async execute(command: DeactiveRoleQueryCommand) {
    this.logger.info(`Async deactive query ${this.aggregateName} cmd ...`);

    const { messagePattern, id, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(await this.repository.findAggregateModelById(id));

    cmdPublisher.updateItem(commandModel);
    cmdPublisher.commit();
  }
}
